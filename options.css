/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f7f8fa;
    color: #333;
    margin: 0;
    padding: 15px;
    width: 450px; /* 增加宽度以适应规则输入 */
    box-sizing: border-box;
}

.container {
    width: 100%;
}

h1 {
    font-size: 20px;
    color: #2c3e50;
    text-align: center;
    margin-top: 0;
    margin-bottom: 20px;
}

h2 {
    font-size: 16px;
    color: #34495e;
    margin-top: 20px;
    margin-bottom: 8px;
}

.section {
    margin-bottom: 15px;
}

.btn {
    padding: 8px 15px;
    font-size: 13px;
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s, border-color 0.2s;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* 输入框和文本域 */
input[type="text"], textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    margin-top: 4px;
}

/* API规则样式 */
.api-rule {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    align-items: center;
}

.api-rule input {
    flex: 1;
    min-width: 300px;
}

.remove-rule-btn {
    background: none;
    border: none;
    color: #dc3545;
    font-size: 16px;
    cursor: pointer;
    padding: 0 5px;
}

.remove-rule-btn:hover {
    color: #c82333;
}

#addRuleBtn {
    margin-top: 8px;
    background-color: #007bff;
    color: white;
}

#addRuleBtn:hover {
    background-color: #0069d9;
}

input[type="text"]:focus, textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.2);
}

textarea {
    resize: vertical;
    font-family: inherit;
}

/* 全局开关容器 */
.global-switch-container {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
}

/* 移除旧的全局开关样式 */
.global-switch {
    display: none;
}

.status {
    margin-top: 10px;
    color: #28a745;
    font-size: 13px;
}

/* Switch toggle styles */
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 24px;
}

.switch input { 
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #28a745;
}

input:checked + .slider:before {
    transform: translateX(16px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}
