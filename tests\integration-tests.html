<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理扩展集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1565c0;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .warning {
            background: #fff3e0;
            border: 1px solid #ff9800;
            color: #ef6c00;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>代理扩展集成测试</h1>
    <p>这个页面测试Chrome扩展的完整功能流程。</p>
    
    <div class="warning">
        <strong>注意:</strong> 这些测试需要Chrome扩展已经加载并运行。请确保：
        <ul>
            <li>扩展已在chrome://extensions中加载</li>
            <li>扩展权限已正确设置</li>
            <li>测试服务器正在运行（如果需要）</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>1. 扩展连接测试</h3>
        <button onclick="testExtensionConnection()">测试扩展连接</button>
        <div id="connection-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 设置存储测试</h3>
        <button onclick="testSettingsStorage()">测试设置存储</button>
        <div id="storage-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 规则匹配测试</h3>
        <button onclick="testRuleMatching()">测试规则匹配</button>
        <div id="matching-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 代理转发测试</h3>
        <button onclick="testProxyForwarding()">测试代理转发</button>
        <div id="proxy-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h3>5. 认证缓存测试</h3>
        <button onclick="testAuthCache()">测试认证缓存</button>
        <div id="auth-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h3>6. 完整流程测试</h3>
        <button onclick="runFullWorkflowTest()">运行完整流程测试</button>
        <div id="workflow-result" class="test-result"></div>
    </div>

    <script>
        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }
        
        function showError(elementId, error) {
            showResult(elementId, `错误: ${error.message || error}`, 'error');
        }
        
        function showSuccess(elementId, message) {
            showResult(elementId, message, 'success');
        }
        
        // 测试1: 扩展连接
        async function testExtensionConnection() {
            showResult('connection-result', '正在测试扩展连接...', 'info');
            
            try {
                if (!chrome || !chrome.runtime) {
                    throw new Error('Chrome扩展API不可用');
                }
                
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ type: 'GET_SETTINGS' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                showSuccess('connection-result', `扩展连接成功！\n当前设置: ${JSON.stringify(response, null, 2)}`);
            } catch (error) {
                showError('connection-result', error);
            }
        }
        
        // 测试2: 设置存储
        async function testSettingsStorage() {
            showResult('storage-result', '正在测试设置存储...', 'info');
            
            try {
                const testSettings = {
                    globalEnable: true,
                    targetHost: 'http://localhost:8080',
                    pathRules: ['/api/', '/test/'],
                    pathRulesEnabled: [true, false]
                };
                
                // 保存设置
                await new Promise((resolve, reject) => {
                    chrome.storage.local.set({ settings: testSettings }, () => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve();
                        }
                    });
                });
                
                // 读取设置
                const result = await new Promise((resolve, reject) => {
                    chrome.storage.local.get('settings', (data) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(data);
                        }
                    });
                });
                
                if (JSON.stringify(result.settings) === JSON.stringify(testSettings)) {
                    showSuccess('storage-result', '设置存储测试通过！\n保存和读取的数据一致');
                } else {
                    throw new Error('保存和读取的数据不一致');
                }
            } catch (error) {
                showError('storage-result', error);
            }
        }
        
        // 测试3: 规则匹配
        async function testRuleMatching() {
            showResult('matching-result', '正在测试规则匹配...', 'info');
            
            try {
                // 设置测试规则
                const testSettings = {
                    globalEnable: true,
                    targetHost: 'http://localhost:8080',
                    pathRules: ['/api/', '/v1/', '/admin/'],
                    pathRulesEnabled: [true, false, true]
                };
                
                await new Promise((resolve) => {
                    chrome.storage.local.set({ settings: testSettings }, resolve);
                });
                
                // 通知background script更新设置
                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        type: 'SETTINGS_UPDATED',
                        payload: testSettings
                    }, resolve);
                });
                
                // 测试不同的URL路径
                const testCases = [
                    { path: '/api/users', shouldMatch: true },
                    { path: '/v1/data', shouldMatch: false },
                    { path: '/admin/settings', shouldMatch: true },
                    { path: '/other/path', shouldMatch: false }
                ];
                
                let results = [];
                for (const testCase of testCases) {
                    // 这里我们无法直接测试background.js的匹配逻辑
                    // 但可以测试前端的逻辑
                    const enabledRules = testSettings.pathRules.filter((rule, index) => 
                        testSettings.pathRulesEnabled[index]
                    );
                    const matches = enabledRules.some(rule => testCase.path.startsWith(rule));
                    
                    results.push(`${testCase.path}: ${matches ? '匹配' : '不匹配'} (期望: ${testCase.shouldMatch ? '匹配' : '不匹配'})`);
                }
                
                showSuccess('matching-result', `规则匹配测试完成！\n${results.join('\n')}`);
            } catch (error) {
                showError('matching-result', error);
            }
        }
        
        // 测试4: 代理转发
        async function testProxyForwarding() {
            showResult('proxy-result', '正在测试代理转发...', 'info');
            
            try {
                // 设置代理配置
                const proxySettings = {
                    globalEnable: true,
                    targetHost: 'http://httpbin.org',
                    pathRules: ['/api/'],
                    pathRulesEnabled: [true]
                };
                
                await new Promise((resolve) => {
                    chrome.storage.local.set({ settings: proxySettings }, resolve);
                });
                
                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        type: 'SETTINGS_UPDATED',
                        payload: proxySettings
                    }, resolve);
                });
                
                // 尝试发送一个测试请求
                try {
                    const response = await fetch('/api/get', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const data = await response.text();
                    showSuccess('proxy-result', `代理转发测试成功！\n响应状态: ${response.status}\n响应数据: ${data.substring(0, 200)}...`);
                } catch (fetchError) {
                    showResult('proxy-result', `代理转发测试完成，但请求失败（这可能是正常的）:\n${fetchError.message}`, 'info');
                }
            } catch (error) {
                showError('proxy-result', error);
            }
        }
        
        // 测试5: 认证缓存
        async function testAuthCache() {
            showResult('auth-result', '正在测试认证缓存...', 'info');
            
            try {
                // 清除现有缓存
                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({ type: 'CLEAR_AUTH_CACHE' }, resolve);
                });
                
                // 设置测试认证
                const testAuth = { 'http://localhost:8080': 'Bearer test-token-123' };
                await new Promise((resolve) => {
                    chrome.storage.local.set({ authCache: testAuth }, resolve);
                });
                
                // 读取认证缓存
                const result = await new Promise((resolve) => {
                    chrome.storage.local.get('authCache', resolve);
                });
                
                if (result.authCache && result.authCache['http://localhost:8080'] === 'Bearer test-token-123') {
                    showSuccess('auth-result', '认证缓存测试通过！\n缓存数据正确保存和读取');
                } else {
                    throw new Error('认证缓存数据不正确');
                }
            } catch (error) {
                showError('auth-result', error);
            }
        }
        
        // 测试6: 完整流程
        async function runFullWorkflowTest() {
            showResult('workflow-result', '正在运行完整流程测试...', 'info');
            
            try {
                let log = [];
                
                // 1. 设置配置
                log.push('1. 设置代理配置...');
                const settings = {
                    globalEnable: true,
                    targetHost: 'http://localhost:9999',
                    pathRules: ['/api/'],
                    pathRulesEnabled: [true]
                };
                
                await new Promise((resolve) => {
                    chrome.storage.local.set({ settings }, resolve);
                });
                
                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        type: 'SETTINGS_UPDATED',
                        payload: settings
                    }, resolve);
                });
                log.push('   ✓ 配置设置完成');
                
                // 2. 清除认证缓存
                log.push('2. 清除认证缓存...');
                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({ type: 'CLEAR_AUTH_CACHE' }, resolve);
                });
                log.push('   ✓ 认证缓存已清除');
                
                // 3. 验证设置
                log.push('3. 验证设置...');
                const currentSettings = await new Promise((resolve) => {
                    chrome.runtime.sendMessage({ type: 'GET_SETTINGS' }, resolve);
                });
                log.push(`   ✓ 当前设置: ${JSON.stringify(currentSettings.settings, null, 2)}`);
                
                showSuccess('workflow-result', `完整流程测试完成！\n\n${log.join('\n')}`);
            } catch (error) {
                showError('workflow-result', error);
            }
        }
        
        // 页面加载时的提示
        window.addEventListener('load', () => {
            console.log('集成测试页面已加载');
            if (!chrome || !chrome.runtime) {
                document.body.innerHTML = '<div class="error">错误: Chrome扩展API不可用。请确保在扩展环境中运行此页面。</div>';
            }
        });
    </script>
</body>
</html>
