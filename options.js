document.addEventListener('DOMContentLoaded', () => {
    const globalEnableSwitch = document.getElementById('globalEnableSwitch');
    const targetHostInput = document.getElementById('targetHostInput');
    const clearCacheBtn = document.getElementById('clearCacheBtn');
    const statusMessage = document.getElementById('statusMessage');
    const apiRulesContainer = document.getElementById('apiRulesContainer');
    const addRuleBtn = document.getElementById('addRuleBtn');

    let settings = {
        globalEnable: false,
        targetHost: '',
        pathRules: [],
        pathRulesEnabled: []
    };

    // 加载配置
    const loadSettings = () => {
        chrome.storage.local.get('settings', (data) => {
            if (data.settings) {
                settings = data.settings;
                // 确保pathRules和pathRulesEnabled存在
                settings.pathRules = settings.pathRules || [];
                settings.pathRulesEnabled = settings.pathRulesEnabled || settings.pathRules.map(() => true);
            }
            render();
        });
    };

    // 保存配置
    const saveSettings = () => {
        // 从UI更新settings对象
        settings.globalEnable = globalEnableSwitch.checked;
        settings.targetHost = targetHostInput.value.trim();

        // 收集路径规则
        settings.pathRules = [];
        settings.pathRulesEnabled = [];
        const ruleElements = apiRulesContainer.querySelectorAll('.api-rule');
        ruleElements.forEach(ruleEl => {
            const pattern = ruleEl.querySelector('.api-pattern').value.trim();
            if (pattern) {
                settings.pathRules.push(pattern);
                settings.pathRulesEnabled.push(true); // 新添加的规则默认启用
            }
        });

        chrome.storage.local.set({ settings }, () => {
            // 通知 background script 更新配置
            chrome.runtime.sendMessage({
                type: 'SETTINGS_UPDATED',
                payload: settings
            });
            statusMessage.textContent = '配置已保存！';
            setTimeout(() => { statusMessage.textContent = ''; }, 2000);
        });
    };

    // 渲染UI
    const render = () => {
        globalEnableSwitch.checked = settings.globalEnable || false;
        targetHostInput.value = settings.targetHost || '';
        
        // 渲染路径规则
        apiRulesContainer.innerHTML = '';
        (settings.pathRules || []).forEach(pattern => {
            addRuleElement(pattern);
        });
        if (apiRulesContainer.children.length === 0) {
            addRuleElement();
        }
    };

    // 添加规则元素
    const addRuleElement = (pattern = '') => {
        const ruleEl = document.createElement('div');
        ruleEl.className = 'api-rule';
        ruleEl.innerHTML = `
            <input type="text" class="api-pattern" value="${pattern}" placeholder="URL Pattern (e.g. https://ipublish-dev.unipus.cn/**)">
            <button class="remove-rule-btn">×</button>
        `;
        apiRulesContainer.appendChild(ruleEl);
        
        // 添加删除事件
        const removeBtn = ruleEl.querySelector('.remove-rule-btn');
        removeBtn.addEventListener('click', () => {
            ruleEl.remove();
            saveSettings();
        });
        
        // 添加输入事件
        const inputs = ruleEl.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', saveSettings);
        });
    };

    // --- 事件监听 ---
    globalEnableSwitch.addEventListener('change', saveSettings);
    targetHostInput.addEventListener('input', saveSettings);
    addRuleBtn.addEventListener('click', () => {
        addRuleElement();
        saveSettings();
    });

    clearCacheBtn.addEventListener('click', () => {
        chrome.storage.local.set({ authCache: {} }, () => {
            statusMessage.textContent = '认证缓存已清除！';
            setTimeout(() => { statusMessage.textContent = ''; }, 2000);
        });
    });

    // 初始化
    loadSettings();
});