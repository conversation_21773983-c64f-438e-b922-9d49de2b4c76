项目名称：开发代理：API转发与认证缓存

版本：2.0.0

描述：
这是一个Chrome浏览器插件，旨在为Web开发者提供强大的API调试能力。它可以拦截页面发出的API请求，根据用户定义的URL路径匹配规则，将其透明地转发到用户指定的单一目标服务器（例如本地开发环境），同时能够自动缓存和附加`Authorization`认证头，极大地简化了需要频繁登录的开发和测试流程。

---

文件结构：

- `manifest.json`: 插件的核心配置文件，定义了所有权限、脚本和基本信息。
- `options.html` / `options.css` / `options.js`: 插件的选项页面，是所有功能配置的图形化管理界面。
- `background.js`: 插件的后台服务脚本，是所有核心逻辑（如请求转发、认证缓存）的处理中心。
- `content.js`: 内容脚本，其唯一作用是向页面注入`interceptor.js`并负责显示“代理已启用”的页面提示。
- `interceptor.js`: 注入到网页中的脚本，通过替换原生的`window.fetch`方法来劫持API请求，并将请求信息发送到`background.js`进行处理。
- `icons/`: 存放插件所需的图标文件。

---

如何加载和使用：

1.  **加载插件**：
    -   打开Chrome浏览器，在地址栏输入 `chrome://extensions` 并回车。
    -   在页面右上角，打开“开发者模式”的开关。
    -   点击左上角的“加载已解压的扩展程序”按钮。
    -   在文件选择窗口中，选择包含本`README.md`文件的 `proxy-ext` 整个文件夹。
    -   插件加载成功后，会出现在扩展程序列表中。

2.  **配置插件**：
    -   点击浏览器工具栏上的插件图标，会直接弹出设置窗口。
    -   **启用API代理**：打开顶部的总开关。
    -   **目标主机**：在输入框中填写所有API请求要转发到的目标地址，例如 `http://localhost:8080` 或 `https://dev.api.example.com`。
    -   **URL路径匹配规则**：在文本域中，每行输入一个您希望代理的URL路径前缀。只有请求的URL路径以这些前缀开头时，才会被代理。例如：
        ```
        /api/
        /v2/users
        /auth/login
        ```
        如果此文本域留空，则表示所有请求都将被代理到目标主机。
    -   **清除认证缓存**：点击按钮可以清除所有已缓存的`Authorization`令牌。

3.  **使用**：
    -   配置完成后，当您访问的网页发出的API请求符合您设置的“URL路径匹配规则”时，这些请求将自动被转发到您指定的目标主机。
    -   如果目标服务器返回了`Authorization`头，插件会自动缓存它。下次对该目标主机的请求将自动附带此认证信息。
    -   当代理功能启用时，页面右上角会短暂显示“开发代理已启用”的提示。