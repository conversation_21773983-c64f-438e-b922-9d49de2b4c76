{"manifest_version": 3, "name": "开发代理：API转发与认证缓存", "version": "2.0.0", "description": "拦截API调用，将其转发到指定主机，并缓存Authorization头。", "permissions": ["storage", "scripting", "activeTab", "webRequest", "declarativeNetRequest", "declarativeNetRequestWithHostAccess", "tabs"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["interceptor.js"], "matches": ["<all_urls>"]}], "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}