2025-06-28 17:49:22,905 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:23,388 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:23,724 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:24,035 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:24,358 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:24,669 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:24,978 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:25,275 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:25,581 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:25,964 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:26,247 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:26,571 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:26,925 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:27,218 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:27,525 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:27,837 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:28,168 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:28,456 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:28.456263",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:28,460 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:28,763 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:28.763905",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:28,767 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:29,094 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:29.094989",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:29,101 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:29,430 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:29.430609",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:29,434 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:29,743 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:29.742290",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:29,746 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:30,049 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:30.049449",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:30,049 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:30,368 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:30.368381",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:30,385 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:30,690 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:30.690146",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:30,694 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:30,992 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:30.992656",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:30,996 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:31,309 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:31.309809",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:31,313 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:31,620 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:31.620366",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:31,623 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:31,967 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:31.967314",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:31,974 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:32,291 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:32.291051",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:32,294 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:32,588 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:32.588483",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:32,588 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:32,877 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:32.877517",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:32,888 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:33,203 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:33.203865",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:33,208 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:33,516 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:33.516566",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:33,520 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:33,830 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:33.830462",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:33,834 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:34,173 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:34.173436",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:34,186 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:34,505 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:34.505597",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:34,511 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:34,806 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:34.806731",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:34,806 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:35,098 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:35.098252",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:35,098 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:35,406 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:35.406289",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:35,422 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:35,716 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:35.716200",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:35,732 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:36,017 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:36.017476",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:36,033 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:36,351 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:36.351170",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:36,355 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:36,634 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:36.634511",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:36,650 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:36,964 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:36.964352",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:36,965 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:37,250 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:37,556 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:37.556808",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:37,572 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:37,866 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:37.866583",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:37,866 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:38,169 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:38.169942",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:38,185 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:38,492 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:38.492948",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:38,496 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:38,796 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:38.796694",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:38,800 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:39,094 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:39.094816",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:39,098 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:39,402 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:39.402807",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:39,402 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:39,708 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:39.708276",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:39,714 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:40,018 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:40.018920",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:40,022 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:40,304 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:40.304479",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:40,320 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:40,633 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:40.632496",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:40,633 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:40,922 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:40.922179",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:40,938 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:41,228 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:41.228862",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:41,228 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:41,523 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:41.523214",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:41,539 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:41,872 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:41.872743",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:41,876 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:42,202 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:42.199662",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:42,205 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:42,508 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:42.508096",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:42,516 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:42,820 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:42.820437",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:42,822 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:43,137 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:43.137009",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:43,146 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:43,449 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:43.449226",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:43,452 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:43,754 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:43.754399",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:43,757 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:44,083 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:44.083574",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:44,086 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:44.086876",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:44,086 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:44,400 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:44.400724",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:44,402 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:44,699 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:44,992 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:44.992837",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:45,005 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:45,303 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:45.303648",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:45,312 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:45,611 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:45.611478",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:45,616 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:45,928 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:45.928164",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:45,930 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:45.930791",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:45,933 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:46,247 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:46.247877",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:46,251 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:46,558 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:46.558435",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:46,567 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:46,891 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:46.891483",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:46,900 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:47,212 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:47.212687",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:47,216 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:47,512 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:47.512827",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:47,519 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:47,856 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:47.856698",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:47,856 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:48,185 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:48.185447",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:48,190 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:48,496 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:48.496066",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:48,499 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:48,788 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:48.787508",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:48,791 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:49,100 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:49.100598",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:49,103 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:49,421 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:49.421120",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:49,425 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:49,780 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:49.780760",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:49,785 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:50,106 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:50.106617",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:50,109 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:50,396 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:50.396686",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:50,403 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:50,743 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:50.743930",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:50,747 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:51,080 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:51.080347",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:51,084 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:51,395 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:51.395965",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:51,400 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:51,711 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:51.711127",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:51,715 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:52,009 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:52.007848",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:52,012 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:52,324 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:52.324713",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:52,332 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:52,618 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:52.618320",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:52,629 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:52,940 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:52.940439",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:52,944 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:53,250 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:53.250473",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:53,255 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:53,567 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:53.567529",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:53,569 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:53,897 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:53.897519",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:53,901 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:54,265 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:54.265601",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:54,269 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:54,557 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:54.557308",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:54,565 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:54,876 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:54.876224",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:54,880 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:55,188 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:55.188025",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:55,195 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:55,520 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:55.520837",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:55,535 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:55,833 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:55.833500",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:55,835 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:56,141 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:56.141712",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:56,145 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:56,473 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:56.473507",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:56,477 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:56,769 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:56.769204",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:56,777 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:57,060 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:57.060457",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:57,073 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:57,386 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:57.386903",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:57,391 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:57,703 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:57.703801",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:57,709 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:58,030 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:58.030734",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:58,034 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:58,333 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:58.333198",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:58,337 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:58,630 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:58.630763",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:58,635 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:58,938 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:58.938246",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:58,944 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:59,263 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:59.263896",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:59,267 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:59,644 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:59.644548",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:59,644 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:49:59,951 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:49:59.951660",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:49:59,954 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:00,264 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:00.264188",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:00,275 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:00,595 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:00.595353",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:00,601 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:00,945 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:00.945776",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:00,954 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:01,268 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:01.268779",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:01,277 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:01,584 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:01.577414",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:01,588 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:01,915 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:01.915269",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:01,928 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:02,261 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:02.261018",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:02,262 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:02,598 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:02.598078",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:02,612 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:02,923 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:02.923090",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:02,927 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:03,254 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:03.254860",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:03,261 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:03,584 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:03.584360",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:03,590 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:03,901 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:03.901856",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:03,907 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:04,253 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:04.253196",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:04,256 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:04,566 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:04.566769",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:04,571 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:04,879 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:04.879796",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:04,884 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:05,210 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:05.210419",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:05,218 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:05,517 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:05.517668",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:05,523 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:05,816 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:05.816920",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:05,822 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:06,119 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:06.119204",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:06,128 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:06,436 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:06.436507",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:06,452 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:06,752 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:06.752721",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:06,761 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:07,061 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:07.061886",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:07,067 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:07,387 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:07.387401",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:07,393 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:07,713 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:07.713528",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:07,716 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:08,025 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:08.025162",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:08,028 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:08,335 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:08.335346",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:08,340 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:08,676 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:08.676409",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:08,680 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:08,987 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:08.987763",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:08,990 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:09,340 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:09.340154",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:09,343 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:09,667 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:09.667881",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:09,671 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:09,994 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:09.994728",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:09,997 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:10,337 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:10.337265",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:10,340 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:10,661 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:10.661144",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:10,664 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:10,992 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:10.992346",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:10,996 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:11,311 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:11.311172",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:11,313 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:11,620 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:11.620465",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:11,625 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:11,928 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:11.928568",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:11,943 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:12,246 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:12.246597",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:12,261 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:12,584 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:12.584063",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:12,588 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:12,925 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:12.925184",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:12,930 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:13,243 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:13.243450",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:13,247 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:13,563 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:13.563784",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:13,567 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:13,865 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:13.865374",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:13,875 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:14,200 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:14.200535",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:14,207 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:14,533 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:14.533211",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:14,539 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:14,832 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:14.832270",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:14,846 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:15,148 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:15.148828",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:15,154 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:15,466 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:15.466517",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:15,474 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:15,814 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:15.814195",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:15,815 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:16,122 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:16.122086",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:16,130 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:16,449 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:16.449842",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:16,458 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:16,771 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:16.771079",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:16,774 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:17,091 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:17.091102",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:17,095 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:17,402 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:17.402695",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:17,415 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:17,724 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:17.724037",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:17,728 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:18,035 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:18.035454",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:18,051 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:18,355 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:18.355419",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:18,360 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:18,682 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:18.682841",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:18,688 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:18,993 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:18.993327",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:18,998 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:19,325 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:19.325009",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:19,330 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:19,667 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:19.667494",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:19,672 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:20,005 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:20.005967",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:20,011 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:20,339 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:20.338622",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:20,343 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:20,719 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:20.719734",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:20,728 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:21,067 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:21.067897",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:21,070 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:21,407 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:21.407144",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:21,407 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:21,722 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:21.722739",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:21,727 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:22,082 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:22.082609",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:22,084 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:22.084615",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:22,090 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:22,421 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:22.421703",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:22,422 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:22,749 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:22.749467",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:22,752 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:23,082 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:23.082026",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:23,086 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:23,391 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:23.391999",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:23,400 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:23,740 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:23.740264",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:23,749 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:24,058 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:24.058883",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:24,072 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:24,377 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:24.377923",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:24,385 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:24,691 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:24.691637",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:24,694 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:25,001 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:25,306 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:25.306226",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:25,311 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:25,629 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:25.629189",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:25,633 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:25,947 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:25.947152",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:25,952 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:26,260 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:26.260624",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:26,264 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:26,562 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:26.562769",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:26,569 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:26,881 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:26.881746",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:26,889 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:27,215 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:27.215346",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:27,225 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:27,548 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:27.548448",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:27,553 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:27,863 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:27.863544",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:27,867 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:28,180 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:28.180698",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:28,188 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:28,509 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:28.509363",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:28,513 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:28,836 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:28.836368",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:28,839 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:29,165 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:29.165415",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:29,169 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:29,489 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:29.489472",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:29,492 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:29,798 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:29.798425",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:29,803 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:30,127 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:30.127244",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:30,132 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:30,449 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:30.449252",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:30,454 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:30,768 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:30.768771",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:30,777 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:31,087 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:31.087386",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:31,088 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:31,395 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:31.395866",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:31,402 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:31,725 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:31.725495",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:31,729 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:32,056 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:32.056941",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:32,060 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:32,382 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:32.382347",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:32,385 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:32,717 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:32.717749",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:32,723 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:33,039 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:33.036425",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:33,042 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:33,357 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:33.357347",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:33,366 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:33,706 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:33.706639",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:33,711 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:34,055 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:34.055564",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:34,059 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:34,423 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:34.423194",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:34,437 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:34,755 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:34.755713",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:34,757 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:35,084 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:35.084819",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:35,088 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:35,391 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:35.391773",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:35,402 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:35,722 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:35.722966",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:35,732 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:36,056 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:36.056716",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:36,059 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:36,389 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:36.389925",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:36,393 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:36,722 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:36.722894",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:36,726 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:36.726633",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:36,729 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:37,040 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:37,349 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:37.349313",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:37,354 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:37,660 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:37.660704",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:37,669 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:37,997 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:37.997607",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:38,011 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:38,312 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:38.312379",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:38,316 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:38,653 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:38.653418",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:38,657 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:38,986 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:38.986035",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:38,990 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:39,361 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:39.359744",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:39,364 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:39,692 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:39.692747",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:39,697 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:39,993 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:39.993111",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:39,997 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:40,323 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:40.323232",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:40,327 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:40,656 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:40.656721",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:40,663 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:41,006 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:41.006294",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:41,009 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:41,322 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:41.322560",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:41,331 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:41,703 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:41.703706",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:41,707 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:42,074 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:42.074249",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:42,077 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:42.077483",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:42,082 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:42,384 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:42.384236",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:42,398 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:42,729 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:42.729062",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:42,733 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:43,085 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:43.085059",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:43,089 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:43,428 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:43.428820",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:43,432 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:43,752 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:43.752789",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:43,760 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:44,119 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:44.119244",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:44,133 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:44,481 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:44.481126",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:44,487 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:44,851 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:44.851029",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:44,854 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:45,189 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:45.189621",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:45,204 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:45,527 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:45.527424",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:45,531 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:45,872 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:45.872187",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:45,872 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:46,224 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:46.224140",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:46,236 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:46,555 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:46.555923",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:46,558 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:46,875 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:46.875099",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:46,876 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:47,190 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:47.190820",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:47,201 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:47,517 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:47.517249",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:47,521 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:47,841 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:47.841521",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:47,844 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:48,160 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:48.160245",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:48,168 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:48,475 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:48.475435",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:48,484 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:48,820 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:48.819351",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:48,823 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:49,165 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:49.165500",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:49,170 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:49,504 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:49.504033",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:49,510 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:49,856 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:49.856392",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:49,861 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:50,171 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:50,480 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:50.480095",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:50,483 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:50,836 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:50.836174",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:50,839 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:51,165 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:51.165703",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:51,170 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:51,502 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:51.502788",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:51,509 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:51,827 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:51.827380",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:51,832 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:52,216 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:52.216054",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:52,217 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:52.217713",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:52,222 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:52,541 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:52.541056",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:52,544 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:52,868 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:52.868705",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:52,872 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:53,201 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:53.200411",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:53,204 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:53,545 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:53.545933",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:53,551 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:53,907 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:53.907474",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:53,911 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:54,234 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:54.233208",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:54,238 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:54,564 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:54.564955",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:54,568 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:54,885 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:54.885950",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:54,897 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:55,221 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:55.221660",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:55,225 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:55,535 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:55.535073",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:55,538 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:55,838 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:55.838220",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:55,846 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:56,174 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:56.174078",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:56,177 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:56,480 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:56,778 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:56.778500",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:56,782 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:57,124 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:57.123843",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:57,127 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:57,427 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:57.427711",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:57,431 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:57,734 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:57.734881",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:57,740 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:58,057 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:58.057314",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:58,061 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:58,450 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:58.450959",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:58,461 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:58,832 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:58.832630",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:58,836 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:59,166 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:59.166507",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:59,172 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:59,486 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:59.486728",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:59,490 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:50:59,797 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:50:59.797462",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:50:59,801 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:00,109 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:00.109210",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:00,110 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:00,442 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:00.442638",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:00,442 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:00,757 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:00.757794",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:00,764 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:01,072 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:01.072932",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:01,077 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:01,385 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:01.385971",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:01,389 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:01,693 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:01.693811",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:01,696 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:02,007 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:02.007000",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:02,010 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:02,312 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:02.310236",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:02,314 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:02,623 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:02.623709",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:02,628 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:02,939 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:02.939897",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:02,943 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:03,262 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:03.262559",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:03,269 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:03,653 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:03.653650",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:03,659 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:04,035 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:04.035607",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:04,046 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:04,406 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:04.406837",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:04,413 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:04,771 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:04.771130",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:04,775 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:05,091 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:05.091670",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:05,097 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:05,427 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:05.426179",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:05,431 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:05,766 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:05.766477",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:05,773 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:05.773408",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:05,777 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:06,098 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:06.098891",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:06,103 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:06,452 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:06.452121",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:06,461 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:06,772 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:06.772229",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:06,775 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:07,106 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:07.106856",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:07,110 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:07,426 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:07.426741",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:07,432 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:07,768 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:07.768154",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:07,782 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:08,104 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:08.104747",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:08,104 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:08,530 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:08.530905",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:08,536 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:08,938 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:08.938931",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:08,943 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:09,345 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:09.345238",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:09,350 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:09,735 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:09.735757",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:09,739 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:10,287 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:10.287535",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:10,290 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:10.290533",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:10,296 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:10,675 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:10.675791",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:10,678 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:11,073 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:11.073912",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:11,078 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:11,517 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:11.515958",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:11,520 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:11.520338",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:11,526 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:11,864 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:11.864239",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:11,868 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:12,187 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:12.186467",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:12,195 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:12,506 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:12.506541",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:12,513 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:12,867 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:12.867470",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:12,871 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:13,266 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:13.266845",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:13,270 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:13,603 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:13.603277",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:13,606 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:13,932 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:14,289 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:14.288198",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:14,293 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:14,640 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:14.640767",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:14,644 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:14,961 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:14.961703",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:14,966 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:15,301 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:15.300392",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:15,305 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:15,822 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:15.822148",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:15,826 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:16,212 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:16.212606",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:16,216 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:16,624 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:16.624117",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:16,628 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:17,131 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:17.131629",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:17,138 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:17,691 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:17.691756",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:17,696 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:17.696392",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:17,700 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:18,217 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:18.217265",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:18,221 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:18,730 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:18.730129",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:18,732 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:18.732147",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:18,737 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:19,282 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:19.282579",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:19,284 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:19,887 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:19.887754",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:19,891 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:19.891103",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:19,903 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:20,580 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:20.577551",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:20,583 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:20.583590",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:20,588 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:21,037 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:21.037362",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:21,037 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:21,368 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:21.368242",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:21,370 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:21,726 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:21.726010",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:21,732 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:22,081 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:22,403 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:22.403963",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:22,415 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:22,745 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:22.745947",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:22,748 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:23,109 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:23,437 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:23.437409",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:23,441 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:23,757 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:23.757313",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:23,759 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:24,102 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:24,498 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:24.498742",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:24,502 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:24,812 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:25,171 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:25.171972",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:25,176 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:25,520 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:25,850 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:25.850425",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:25,854 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:26,194 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:26.193698",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:26,198 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:26,533 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:26.533817",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:26,536 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:26,866 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:26.866990",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:26,871 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:27,171 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:27.171480",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:27,186 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:27,521 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:27.521268",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:27,525 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:27,841 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:27.841866",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:27,845 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:28,185 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:28.184453",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:28,188 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:28,488 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:28.488904",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:28,490 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:28,782 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:28.782417",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:28,794 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:29,103 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:29.103792",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:29,116 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:29,485 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:29,783 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:29.783189",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:29,787 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:30,101 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:30.101657",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:30,107 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:30,445 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:30.445702",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:30,451 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:30,791 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:30.791950",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:30,796 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:31,148 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:31.148693",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:31,159 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:31,572 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:31.572157",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:31,575 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:31.575501",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:31,580 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:31,954 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:31.954965",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:31,959 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:32,286 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:32.286800",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:32,293 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:32,614 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:32.614561",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:32,614 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:32,945 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:32.945196",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:32,948 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:33,268 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:33.268800",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:33,271 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:33,587 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:33.587441",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:33,591 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:33,925 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:33.922576",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:33,927 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:34,257 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:34.257369",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:34,262 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:34,645 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:34.645076",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:34,650 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:34,986 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:34.986884",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:34,990 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:35,289 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:35.289900",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:35,297 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:35,633 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:35.633658",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:35,638 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:35,967 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:35.967682",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:35,972 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:36,334 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:36.334992",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:36,339 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:36,657 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:36.657230",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:36,661 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:36,974 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:36.974405",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:36,989 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:37,306 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:37.306023",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:37,309 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:37,624 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:37.624714",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:37,635 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:37,962 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:37.962898",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:37,966 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:38,314 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:38.314073",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:38,319 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:38,669 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:38.669683",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:38,678 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:39,085 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:39.083325",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:39,089 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:39,474 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:39.474013",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:39,474 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:39,784 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:39.784531",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:39,787 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:40,144 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:40.144606",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:40,149 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:40,467 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:40.467788",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:40,470 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:40,805 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:40.805744",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:40,810 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:41,214 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:41.214081",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:41,218 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:41,526 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:41.526106",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:41,529 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:41,851 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:41.851427",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:41,855 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:42,173 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:42,554 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:42.554569",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:42,559 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:42.559446",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:42,564 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:42,948 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:42.948796",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:42,951 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:43,318 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:43.318902",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:43,321 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:43.321359",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:43,327 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:43,663 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:44,011 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:44.011224",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:44,018 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:44,367 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:44,729 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:44.727543",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:44,733 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:44.733722",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:44,739 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:45,102 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:45,434 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:45.434127",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:45,437 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:45.437228",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:45,440 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:45,809 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:46,153 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:46.153455",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:46,154 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:46.154499",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:46,164 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:46,504 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:46,825 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:46.825376",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:46,828 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:46.828462",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:46,832 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:47,131 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:47,469 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:47.469569",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:47,474 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:47,802 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:48,107 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:48.107969",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:48,109 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:48.109975",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:48,116 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:48,461 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:48,756 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:48.756104",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:48,764 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:48.764394",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:48,768 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:49,067 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:49,386 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:49.386692",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:49,390 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:49.390317",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:49,394 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:49,691 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:49,997 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:49.997292",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:50,000 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:50.000407",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:50,003 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:50,312 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:50,613 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:50.613456",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:50,622 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:50.622259",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:50,626 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:50,928 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:51,228 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:51.223243",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:51,230 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:51.228850",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:51,232 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:51,522 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:51,866 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:51.866511",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:51,868 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:51.868944",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:51,872 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:52,175 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:52,478 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:52.478393",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:52,485 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:52.485015",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:52,489 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:52,823 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:53,146 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:53.145654",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:53,148 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:53.148653",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:53,152 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:53,460 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:53,757 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:53.757260",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:53,757 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:53.757260",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:53,761 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:54,061 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:54,407 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:54.407377",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:54,407 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:54.407377",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:54,414 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:54,716 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:55,013 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:55.013261",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:55,031 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:55,373 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:55.373625",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:55,375 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:55,714 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:55.714176",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:55,718 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:56,041 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:56.041440",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:56,042 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:56,391 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:56.391912",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:56,396 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:56,691 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:56.691685",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:56,694 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:56,987 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:56.987107",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:56,990 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:57,292 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:57.292468",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:57,294 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:57,606 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:57.606225",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:57,609 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:57,926 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:57.926305",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:57,927 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:58,294 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:58.294509",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:58,298 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:58,604 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:58.604336",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:58,610 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:58,915 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:58.915480",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:58,918 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:59,234 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:59.234074",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:59,237 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:59,542 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:59.542071",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:59,553 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:51:59,857 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:51:59.857885",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:51:59,858 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:00,144 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:00.144277",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:00,144 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:00,470 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:00.470386",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:00,470 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:00,769 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:00.769688",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:00,778 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:01,085 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:01.085484",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:01,087 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:01,388 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:01,694 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:01.694946",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:01,697 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:01.697423",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:01,698 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:01,995 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:02,288 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:02.288346",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:02,291 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:02.291166",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:02,296 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:02,596 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:02,900 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:02.900510",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:02,903 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:03,218 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:03.218253",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:03,219 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:03,532 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:03.532336",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:03,537 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:03,832 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:04,145 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:04.145284",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:04,148 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:04.148856",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:04,153 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:04,458 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:04,784 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:04.784431",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:04,788 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:04.788456",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:04,791 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:05,074 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:05,386 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:05.383495",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:05,387 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:05.387901",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:05,392 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:05,707 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:05,993 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:05.993479",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:06,008 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:06.008727",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:06,013 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:06,376 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:06,685 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:06.685225",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:06,687 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:06.687234",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:06,691 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:06.691249",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:06,695 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:07,013 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:07,311 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:07.311133",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:07,314 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:07.314660",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:07,320 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:07,634 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:07,973 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:07.973663",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:07,978 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:07.978079",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:07,983 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:08,295 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:08,612 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:08.612466",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:08,614 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:08.614501",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:08,616 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:08.616546",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:08,620 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:08,936 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:09,250 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:09.250121",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:09,250 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:09.250121",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:09,256 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:09,570 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:09,884 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:09.884760",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:09,886 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:09.886788",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:09,891 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:10,228 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:10,578 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:10.578853",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:10,580 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:10.580365",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:10,583 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:10,899 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:11,220 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:11.220652",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:11,223 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:11.223920",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:11,227 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:11,572 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:11,912 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:11.911339",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:11,914 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:11.914338",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:11,921 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:12,342 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:12,865 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:12.865035",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:12,867 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:12.867044",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:12,875 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:13,379 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:13.379384",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:13,382 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:13.382294",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:13,385 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:13,837 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:13.837546",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:13,842 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:13.842894",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:13,842 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:14,305 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:14.305568",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:14,322 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:14,720 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:14.719041",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:14,724 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:15,063 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:15.063756",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:15,064 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:15,414 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:15.414819",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:15,419 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:15,797 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:15.797685",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:15,800 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:16,174 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:16.174396",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:16,178 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:16,529 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:16.529329",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:16,531 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:16,937 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:16.937497",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:16,939 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:16.939649",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:16,945 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:17,333 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:17.333663",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:17,335 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:17,698 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:18,035 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:18.034704",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:18,038 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:18,387 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:18.387941",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:18,393 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:18,740 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:19,122 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:19.122608",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:19,122 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:19,442 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:19.442743",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:19,458 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:19,772 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:19.772290",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:19,775 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:20,080 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:20,416 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:20.416024",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:20,419 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:20.419906",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:20,423 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:20,738 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:21,048 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:21.048014",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:21,052 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:21,374 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:21.374423",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:21,376 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:21,683 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:21.683729",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:21,690 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:21,998 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:21.998588",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:22,000 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:22,315 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:22,622 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:22.622317",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:22,624 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:22,916 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:22.916478",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:22,932 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:23,248 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:23.248429",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:23,251 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:23,550 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:23.550268",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:23,550 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:23,870 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:23.870130",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:23,873 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:24,186 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:24.186806",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:24,190 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:24,532 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:24.532077",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:24,535 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:24,849 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:24.849922",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:24,852 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:25,146 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:25.146484",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:25,148 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:25,453 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:25.453944",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:25,456 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:25,762 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:25.762651",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:25,769 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:26,063 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:26.063427",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:26,068 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:26,381 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:26.374754",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:26,383 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:26,689 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:26.689763",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:26,693 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:26,982 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:27,281 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:27.281718",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:27,285 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:27,580 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:27.580249",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:27,594 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:27,889 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:27.889248",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:27,893 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:28,189 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:28.189315",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:28,191 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:28,491 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:28.491658",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:28,496 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:28,797 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:28.797468",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:28,798 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:29,082 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:29.082524",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:29,096 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:29,418 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:29.418755",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:29,418 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:29,716 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:29.716380",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:29,725 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:30,010 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:30.010156",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:30,022 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:30,343 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:30.343407",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:30,354 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:30,662 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:30.660790",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:30,662 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:30,942 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:30.942917",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:30,959 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:31,255 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:31.255061",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:31,256 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:31,562 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:31.555632",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:31,564 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:31,868 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:31.868392",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:31,868 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:32,170 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:32.170119",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:32,177 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:32,476 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:32.476213",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:32,481 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:32,782 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:32.782675",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:32,787 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:33,102 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:33.102304",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:33,105 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:33,454 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:33.449888",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:33,454 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:33,750 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:33.750129",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:33,754 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:34,065 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:34.065003",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:34,067 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:34,397 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:34.397154",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:34,407 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:34.407337",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:34,409 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:34,724 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:34.724044",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:34,727 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:35,115 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:35.115972",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:35,119 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:35,424 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:35.424781",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:35,427 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:35,726 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:35.726953",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:35,726 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:36,018 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:36.018688",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:36,033 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:36,353 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:36.351636",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:36,354 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:36,675 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:36.675089",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:36,678 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:36,976 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:36.976182",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:36,980 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:37,312 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:37.312371",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:37,312 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:37,635 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:37.635313",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:37,639 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:37,936 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:37.936758",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:37,942 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:38,252 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:38.252201",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:38,254 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:38,613 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:38.613415",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:38,616 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:38,959 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:38.959942",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:38,963 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:39,268 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:39.268306",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:39,271 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:39,598 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:39.598938",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:39,598 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:39,959 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:39.959050",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:39,964 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:40,273 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:40.273689",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:40,283 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:40,595 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:40.594094",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:40,597 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:40,894 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:40.894878",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:40,907 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:41,183 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:41.183394",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:41,200 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:41,507 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:41.507780",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:41,509 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:41,813 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:41.813592",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:41,816 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:42,111 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:42.111708",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:42,113 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:42,415 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:42.415210",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:42,419 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:42,739 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:42.739267",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:42,742 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:43,051 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:43.051148",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:43,053 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:43,353 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:43.353193",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:43,368 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:43,671 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:43.671801",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:43,674 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:43,970 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:43.970764",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:43,973 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:44,295 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:44.285977",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:44,296 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:44,592 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:44,896 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:44.896347",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:44,900 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:45,200 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:45.199051",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:45,202 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:45,524 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:45.524582",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:45,527 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:45,839 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:46,144 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:46.143201",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:46,148 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:46.148703",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:46,151 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:46,467 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:46,771 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:46.771313",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:46,774 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:46.774374",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:46,781 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:47,087 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:47.087283",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:47,089 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:47,404 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:47.404513",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:47,416 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:47.416471",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:47,421 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:47,718 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:48,019 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:48.019231",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:48,025 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:48,344 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:48.344185",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:48,347 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:48,653 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:48.653245",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:48,656 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:48,965 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:49,296 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:49.296475",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:49,300 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:49.300351",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:49,303 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:49.303572",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:49,307 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:49,641 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:49,944 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:49.944892",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:49,948 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:49.948896",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:49,951 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:50,261 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:50,589 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:50.588352",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:50,592 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:50.592362",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:50,596 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:50,880 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:51,200 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:51.200884",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:51,204 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:51.204413",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:51,210 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:51,530 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:51,832 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:51.832846",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:51,836 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:51.836863",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:51,840 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:52,153 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:52,451 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:52.451468",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:52,454 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:52.454677",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:52,459 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:52,750 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:53,056 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:53.056841",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:53,060 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:53.060645",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:53,063 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:53,356 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:53,697 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:53.697078",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:53,701 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:53.701005",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:53,703 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:53.703146",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:53,707 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:54,023 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:54,329 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:54.329451",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:54,332 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:54.332390",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:54,336 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:54,667 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:54,981 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:54.981157",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:54,982 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:54.982985",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:54,986 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:55,301 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:55,606 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:55.606281",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:55,608 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:55.608924",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:55,612 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:55,916 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:56,225 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:56.224443",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:56,227 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:56.227807",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:56,232 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:56,604 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:56,901 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:56.901464",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:56,903 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:56.903688",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:56,908 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:57,232 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:57,555 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:57.555939",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:57,558 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:57.558482",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:57,562 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:57,890 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:58,190 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:58.190042",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:58,192 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:58.192174",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:58,195 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:58,507 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:58,803 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:58.803706",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:58,815 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:58.815694",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:58,819 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:59,099 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:59,437 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:59.437518",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:59,441 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:52:59,747 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:52:59.747860",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:52:59,757 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:00,051 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:00.051214",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:00,053 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:00.053280",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:00,057 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:00,397 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:00,687 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:00.687449",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:00,692 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:01,014 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:01.014588",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:01,017 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:01,337 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:01.336608",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:01,340 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:01.340670",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:01,345 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:01,649 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:01,956 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:01.956154",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:01,968 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:02,343 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:02.343762",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:02,345 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:02,661 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:02.661865",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:02,666 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:02,979 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:02.979426",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:02,981 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:03,293 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:03.293841",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:03,295 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:03,617 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:03,922 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:03.922819",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:03,937 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:03.937931",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:03,941 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:04,257 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:04,572 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:04.572690",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:04,579 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:04.579688",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:04,582 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:04,896 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:05,202 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:05.202632",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:05,206 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:05.206118",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:05,209 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:05,508 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:05,827 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:05.827360",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:05,830 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:05.830719",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:05,833 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:06,176 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:06,475 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:06.475443",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:06,481 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:06.481311",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:06,483 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:06.483233",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:06,490 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:06,773 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:07,101 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:07.101107",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:07,105 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:07,410 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:07.408549",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:07,411 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:07,730 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:07.730431",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:07,734 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:08,075 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:08.075204",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:08,087 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:08,397 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:08.397466",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:08,404 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:08,707 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:09,012 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:09.012797",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:09,015 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:09.015803",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:09,017 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:09.017801",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:09,021 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:09,362 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:09,682 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:09.682373",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:09,684 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:09.684859",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:09,688 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:09,999 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:10,345 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:10.345025",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:10,346 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:10.346536",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:10,350 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:10,674 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:10,982 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:10.982378",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:10,993 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:10.993204",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:10,999 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:11,326 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:11,681 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:11.681612",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:11,683 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:11.683929",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:11,688 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:12,023 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:12,349 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:12.349139",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:12,356 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:12,709 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:12.709602",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:12,716 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:13,054 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:13.054017",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:13,054 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:13,377 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:13.377571",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:13,378 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:13,713 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:13.713647",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:13,717 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:13.717070",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:13,720 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:14,053 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:14.053381",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:14,056 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:14.056966",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:14,061 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:14,390 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:14,695 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:14.695278",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:14,697 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:15,008 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:15.008415",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:15,010 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:15,305 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:15,613 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:15.613779",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:15,613 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:15.613779",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:15,618 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:15,930 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:16,243 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:16.243705",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:16,248 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:16,566 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:16.566473",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:16,569 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:16,873 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:16.873402",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:16,888 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:16.888427",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:16,891 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:17,207 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:17,531 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:17.531581",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:17,535 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:17,844 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:17.842556",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:17,846 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:18,167 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:18.167103",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:18,170 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:18,482 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:18,823 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:18.823015",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:18,828 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:18.828555",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:18,832 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:19,153 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:19.144253",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:19,155 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:19,524 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:19.523974",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:19,529 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:19,845 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:19.845875",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:19,848 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:20,159 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:20.159590",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:20,163 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:20,477 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:20,812 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:20.812320",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:20,812 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:20.812320",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:20,824 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:21,129 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:21.129864",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:21,132 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:21,446 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:21.446846",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:21,451 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:21,745 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:21.745424",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:21,755 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:22,062 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:22.062396",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:22,067 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:22,394 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:22,681 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:22.681725",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:22,681 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:22.681725",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:22,694 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:23,015 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:23.015988",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:23,020 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:23,316 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:23.316136",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:23,330 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:23,641 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:23.641708",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:23,644 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:23,956 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:23.956574",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:23,959 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:24,280 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:24,634 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:24.634745",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:24,637 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:24.637742",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:24,641 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:24,977 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:25,342 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:25.342491",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:25,345 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:25.345490",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:25,349 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:25,639 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:25,946 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:25.946368",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:25,950 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:25.950922",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:25,955 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:26,268 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:26,592 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:26.591708",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:26,595 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:26.595712",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:26,598 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:26,922 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:27,304 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:27.304169",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:27,307 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:27.307535",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:27,311 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:27,643 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:27,992 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:27.992092",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:27,994 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:27.994868",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:27,996 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:28,323 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:28,645 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:28.645986",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:28,649 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:28.649211",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:28,652 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:28.652175",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:28,657 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:28,988 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:29,341 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:29.341742",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:29,345 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:29.345266",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:29,348 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:29,675 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:30,059 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:30.059066",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:30,061 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:30.061065",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:30,065 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:30,408 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:30,729 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:30.729666",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:30,731 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:30.731844",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:30,736 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:31,060 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:31,375 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:31.375052",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:31,383 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:31.383229",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:31,388 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:31,725 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:32,092 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:32.092353",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:32,095 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:32.095862",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:32,099 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:32,426 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:32,729 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:32.729499",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:32,742 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:32.742399",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:32,746 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:33,046 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:33,394 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:33.394579",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:33,402 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:33.402465",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:33,406 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:33,746 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:34,076 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:34.076904",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:34,078 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:34.078717",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:34,084 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:34,395 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:34,712 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:34.712874",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:34,712 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:34.712874",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:34,727 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:35,040 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:35,432 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:35.432156",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:35,435 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:35.435797",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:35,439 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:35,755 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:35.755192",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:35,760 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:35.760937",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:35,763 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:36,100 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:36,464 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:36.464820",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:36,470 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:36.470730",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:36,475 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:36,765 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:37,074 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:37,388 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:37.388133",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:37,388 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:37.388497",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:37,393 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:37,717 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:38,031 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:38.031683",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:38,034 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:38.034284",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:38,037 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:38,351 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:38,671 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:38.671352",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:38,674 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:38.674788",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:38,678 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:39,094 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:39,437 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:39.437082",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:39,440 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:39.440455",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:39,445 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:39,777 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:40,103 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:40.103141",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:40,104 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:40.104721",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:40,109 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:40,438 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:40,760 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:40.760120",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:40,766 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:40.766838",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:40,773 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:41,089 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:41,435 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:41.435291",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:41,440 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:41,755 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:42,108 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:42.108650",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:42,113 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:42.113238",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:42,116 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:42.116162",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:42,121 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:42,444 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:42,782 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:42.781336",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:42,784 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:42.784456",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:42,788 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:43,117 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:43,459 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:43.459153",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:43,462 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:43.462877",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:43,466 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:43,787 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:44,110 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:44.110950",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:44,118 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:44.118804",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:44,124 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:44,461 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:44,779 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:44.779791",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:44,779 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:44.779791",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:44,787 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:45,095 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:45,429 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:45.429531",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:45,431 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:45.431536",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:45,435 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:45,730 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:46,053 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:46.053143",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:46,053 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:46.053143",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:46,057 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:46,369 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:46,678 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:46.678404",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:46,686 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:46.686234",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:46,691 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:46,998 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:47,297 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:47.297882",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:47,308 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:47.308923",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:47,315 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:47,663 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:47,988 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:47.988292",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:47,994 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:47.994195",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:48,000 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:48,303 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:48,604 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:48.604402",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:48,606 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:48.606405",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:48,609 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:48,935 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:49,266 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:49.265473",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:49,268 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:49.268472",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:49,272 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:49,585 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:49,918 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:49.918270",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:49,919 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:49.919686",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:49,925 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:50,241 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:50,569 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:50.568243",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:50,571 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:50.571257",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:50,574 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:50,897 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:51,237 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:51.237072",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:51,242 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:51,576 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:51.576945",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:51,579 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:51,912 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:51.912580",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:51,916 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:52,271 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:52.271425",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:52,279 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:52,588 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:52.588707",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:52,592 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:52,894 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:53,246 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:53.246693",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:53,251 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:53.251418",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:53,255 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:53,553 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:53.553879",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:53,556 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:53,880 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:53.880609",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:53,884 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:54,199 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:54,530 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:54.530225",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:54,538 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:54.538116",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:54,543 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:54,835 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:55,119 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:55.119702",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:55,130 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:55.130912",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:55,133 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:55.133066",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:55,137 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:55,429 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:55,727 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:55.727393",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:55,734 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:56,022 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:56.022204",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:56,028 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:56,323 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:56.323743",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:56,328 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:56,621 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:56,926 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:56.926578",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:56,929 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:56.929328",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:56,931 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:57,223 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:57,534 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:57.534053",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:57,538 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:57,857 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:58,204 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:58.204438",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:58,210 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:58.210782",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:58,210 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:58,500 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:58.500034",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:58,503 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:58,795 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:58.795562",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:58,800 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:59,132 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:59.132070",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:59,138 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:59,480 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:53:59.480562",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:53:59,486 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:53:59,786 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:00,079 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:00.079912",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:00,095 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:00.095715",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:00,099 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:00,485 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:00,762 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:00.762471",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:00,777 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:00.777564",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:00,780 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:00.780621",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:00,785 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:01,116 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:01,418 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:01.418528",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:01,421 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:01.421740",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:01,423 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:01.423837",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:01,427 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:01,744 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:02,067 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:02.067496",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:02,069 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:02.069874",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:02,073 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:02,359 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:02,667 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:02.667508",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:02,672 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:02,990 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:03,336 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:03.336121",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:03,339 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:03.339172",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:03,342 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:03,635 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:03,952 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:03.952887",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:03,954 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:03.954908",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:03,959 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:04,301 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:04,644 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:04.644104",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:04,654 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:04.654374",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:04,657 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:04.657372",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:04,662 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:04,967 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:05,284 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:05.284276",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:05,287 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:05.287129",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:05,290 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:05,603 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:05,923 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:05.923572",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:05,926 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:05.926165",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:05,930 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:06,233 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:06,543 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:06.543053",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:06,545 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:06.545050",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:06,549 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:06,856 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:07,152 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:07.152664",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:07,157 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:07,465 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:07.465946",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:07,468 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:07,783 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:07.783210",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:07,785 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:07.785208",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:07,789 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:08,108 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:08,409 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:08.409299",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:08,412 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:08.412204",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:08,416 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:08,709 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:09,019 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:09.019636",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:09,022 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:09.022222",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:09,026 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:09,340 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:09,659 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:09.659843",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:09,664 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:09.664923",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:09,666 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:09,975 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:10,279 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:10,602 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:10.602621",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:10,605 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:10.605621",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:10,608 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:10,927 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:10.927406",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:10,929 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:11,241 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:11.241007",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:11,246 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:11,569 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:11.569163",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:11,570 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:11,877 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:11.877125",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:11,881 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:12,189 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:12.189641",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:12,191 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:12,496 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:12.496353",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:12,501 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:12,830 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:12.830973",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:12,830 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:13,196 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:13.196667",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:13,200 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:13,497 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:13.497388",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:13,497 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:13,851 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:13.851282",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:13,855 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:14,160 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:14,468 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:14.468615",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:14,468 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:14.468615",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:14,476 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:14,775 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:14.775688",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:14,785 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:15,101 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:15.101497",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:15,111 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:15,404 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:15.404411",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:15,404 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:15,722 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:15.717747",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:15,725 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:16,036 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:16.036348",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:16,038 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:16,337 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:16.337732",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:16,342 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:16,670 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:16,956 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:16.956296",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:16,967 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:16.967251",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:16,972 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:17,325 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:17,655 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:17.655265",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:17,655 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:17.655265",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:17,669 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:18,033 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:18.033263",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:18,033 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:18,568 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:18.568216",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:18,571 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:18.571002",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:18,575 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:19,093 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:19,706 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:19.706473",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:19,709 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:19.709976",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:19,711 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:19.711498",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:19,719 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:20,263 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:20,784 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:20.784094",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:20,786 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:20.786904",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:20,792 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:20.792326",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:20,792 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:20.792326",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:20,800 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:21,326 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:21,928 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:21.928123",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:21,932 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:21.932942",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:21,932 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:21.932942",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:21,943 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:22,465 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:22,988 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:22.988088",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:22,996 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:22.996748",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:22,996 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:22.996748",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:23,007 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:23,489 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:23,799 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:23.799953",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:23,808 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:23.808945",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:23,811 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:23.811345",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:23,816 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:24,127 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:24,477 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:24,790 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:24.790860",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:24,792 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:24.792490",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:24,794 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:25,105 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:25,403 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:25,719 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:26,036 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:26.036210",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:26,039 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:26.039738",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:26,041 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:26,339 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:26,682 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:26,990 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:26.990890",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:26,992 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:26.992679",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:26,995 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:27,305 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:27,593 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:27,907 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:28,230 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:28.230469",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:28,233 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:28.233114",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:28,237 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:28,534 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:28,824 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:28.824907",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:28,828 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:28.828476",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:28,831 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:29,163 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:29,493 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:29.493363",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:29,495 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:29,857 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:30,215 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:30.215562",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:30,219 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:30.219039",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:30,224 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:30,593 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:30,945 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:30.945083",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:30,948 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:30.948880",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:30,956 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:31,331 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:31,926 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:31.926463",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:31,931 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:31.931761",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:31,937 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:31.937188",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:31,944 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:32,687 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:33,319 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:33.319959",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:33,324 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:33.323644",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:33,327 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:33.327275",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:33,333 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:33.333615",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:33,337 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:33.337237",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:33,342 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:33,962 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:34,628 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:34.628941",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:34,635 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:34.635084",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:34,640 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:34.640382",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:34,642 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:34.642993",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:34,649 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:35,372 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:36,075 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:36.075965",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:36,081 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:36.081979",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:36,086 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:36.086971",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:36,086 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:36.086971",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:36,098 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:36,708 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:37,333 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:37.333126",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:37,339 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:37.339134",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:37,342 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:37.342271",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:37,349 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:38,098 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:38,718 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:38.718446",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:38,721 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:38.721918",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:38,725 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:39,338 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:40,010 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:40.010083",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:40,016 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:40.014935",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:40,027 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:40,748 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:41,460 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:41.460452",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:41,463 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:41.463906",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:41,470 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:42,198 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:42,890 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:42.890656",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:42,894 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:42.894828",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:42,903 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:43,695 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:44,477 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:44.477886",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:44,482 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:44.481095",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:44,490 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:45,341 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:45.341944",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:45,346 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:45.346105",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:45,349 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:46,215 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:47,001 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:47.001239",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:47,008 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:47,683 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:47.683727",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:47,687 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:48,117 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:48.117051",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:48,124 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:48,545 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:48,932 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:48.932721",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:48,937 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:49,337 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:49,797 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:49.797222",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:49,801 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:50,172 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:50,514 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:50.514567",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:50,518 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:50,906 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:51,276 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:51.276656",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:51,285 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:51,651 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:52,019 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:52.019752",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:52,025 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:52,442 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:52,809 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:52.809211",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:52,816 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:53,258 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:53,655 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:53.655273",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:53,661 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:53.661322",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:53,668 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:54,076 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:54.076842",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:54,079 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:54,515 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:54.515313",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:54,518 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:54,969 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:54.969168",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:54,972 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:55,457 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:55.457469",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:55,463 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:55,859 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:56,197 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:56.197970",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:56,203 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:56.203305",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:56,209 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:56,533 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:56,878 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:56.877344",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:56,881 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:56.881854",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:56,886 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:57,252 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:57,634 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:57.634515",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:57,639 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:57.639587",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:57,645 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:57,970 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:58,311 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:58.311934",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:58,317 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:58.316339",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:58,323 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:58,712 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:59,072 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:59.072851",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:59,074 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:59.074862",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:59,084 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:59,418 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:54:59,766 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:54:59.766040",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:54:59,771 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:00,097 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:00,412 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:00.412325",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:00,416 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:00,717 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:01,050 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:01.050484",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:01,054 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:01.054557",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:01,060 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:01,453 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:01,788 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:01.788957",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:01,792 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:01.792216",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:01,797 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:02,169 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:02,537 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:02.537137",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:02,539 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:02.539648",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:02,545 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:02,931 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:03,318 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:03.318259",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:03,321 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:03.319543",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:03,325 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:03.323551",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:03,327 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:03.327902",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:03,334 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:03,693 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:04,086 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:04.086425",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:04,089 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:04.089156",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:04,093 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:04,477 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:04,890 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:04.890149",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:04,894 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:04.892163",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:04,898 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:05,237 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:05,590 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:05.590598",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:05,594 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:05.594634",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:05,599 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:05,948 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:06,309 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:06.309947",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:06,314 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:06.314066",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:06,320 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:06,667 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:07,001 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:07.001374",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:07,006 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:07.006807",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:07,010 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:07,328 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:07,650 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:07.650277",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:07,653 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:07.653831",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:07,658 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:07,978 - INFO - Received request:
{
  "method": "GET",
  "path": "docs/"
}
2025-06-28 17:55:08,333 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:08.333347",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:08,335 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:08.335069",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:08,343 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:08.343319",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:08,348 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104302124 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:08,350 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:08.349973",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn",
    "content-length": "12",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"\"}"
}
2025-06-28 17:55:08,351 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104302124 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:08,354 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:08.354223",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn, uvicorn",
    "content-length": "12",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"\"}"
}
2025-06-28 17:55:08,357 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104302124 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:08,359 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:08.359558",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn, uvicorn, uvicorn",
    "content-length": "12",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"\"}"
}
2025-06-28 17:55:08,362 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104302124 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:08,364 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:08.364390",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn, uvicorn, uvicorn, uvicorn",
    "content-length": "12",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"\"}"
}
2025-06-28 17:55:08,367 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104302124 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:08,369 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:08.369155",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn, uvicorn, uvicorn, uvicorn, uvicorn",
    "content-length": "12",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"\"}"
}
2025-06-28 17:55:08,371 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104302124 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:08,373 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:08.372834",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn, uvicorn, uvicorn, uvicorn, uvicorn, uvicorn",
    "content-length": "12",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"\"}"
}
2025-06-28 17:55:09,488 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:09.488705",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:09,916 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:09.914843",
  "error": "",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:10,355 - ERROR - Error processing request:
{
  "timestamp": "2025-06-28T17:55:10.355891",
  "error": "All connection attempts failed",
  "request": {
    "method": "GET",
    "path": "docs/"
  }
}
2025-06-28 17:55:10,358 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104162877 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:10,358 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:10.358454",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn",
    "content-length": "42",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"All connection attempts failed\"}"
}
2025-06-28 17:55:10,358 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104162877 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:10,364 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:10.363657",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn, uvicorn",
    "content-length": "42",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"All connection attempts failed\"}"
}
2025-06-28 17:55:10,366 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104162877 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:10,367 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:10.367374",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn, uvicorn, uvicorn",
    "content-length": "42",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"All connection attempts failed\"}"
}
2025-06-28 17:55:10,369 - INFO - HTTP Request: GET http://127.0.0.1:9999/docs/?id=5e92b855-5c32-4929-a543-f53fc0b2859a&vscodeBrowserReqId=1751104162877 "HTTP/1.1 500 Internal Server Error"
2025-06-28 17:55:10,370 - INFO - Response:
{
  "timestamp": "2025-06-28T17:55:10.370490",
  "status_code": 500,
  "headers": {
    "date": "Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:02 GMT, Sat, 28 Jun 2025 09:55:07 GMT",
    "server": "uvicorn, uvicorn, uvicorn, uvicorn",
    "content-length": "42",
    "content-type": "application/json",
    "connection": "close"
  },
  "content": "{\"error\":\"All connection attempts failed\"}"
}
