const originalFetch = window.fetch;

window.fetch = async (resource, config) => {
  try {
    const url = (resource instanceof Request) ? resource.url : String(resource);
    const body = (resource instanceof Request) ? await resource.clone().text() : config?.body;

    // 从storage读取URL匹配规则
    const { apiRules } = await chrome.storage.local.get('apiRules');
    
    // 检查URL是否匹配任何规则
    const shouldProxy = apiRules && Array.isArray(apiRules) && 
      apiRules.some(pattern => url.includes(pattern));
    
    // 将fetch的参数打包，通过消息发送给background.js进行处理
    const response = await chrome.runtime.sendMessage({
      type: 'PROXY_FETCH',
      payload: {
        url: url,
        config: {
          method: (resource instanceof Request) ? resource.method : config?.method || 'GET',
          headers: {
            ...Object.fromEntries(new Headers(config?.headers).entries()),
            'X-Original-URL': url
          },
          body: body
        },
        shouldProxy: shouldProxy
      }
    });

    // 如果background返回错误，则抛出，并回退到原始fetch
    if (!response || response.error) {
      throw new Error(response?.details || '代理请求失败');
    }

    // 如果background决定不处理此请求，则使用原始fetch
    if (response.action === 'PROCEED_ORIGINAL') {
      return originalFetch(resource, config);
    }

    // 使用background返回的数据，重建一个Response对象并返回给页面
    return new Response(response.data.body, {
      status: response.data.status,
      statusText: response.data.statusText,
      headers: response.data.headers
    });

  } catch (error) {
    console.error('开发代理插件错误:', error);
    // 发生任何异常时，调用原始fetch，确保不破坏页面功能
    return originalFetch(resource, config);
  }
};
