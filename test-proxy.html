<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .error {
            background: #ffe6e6;
            color: #d00;
        }
        .success {
            background: #e6ffe6;
            color: #080;
        }
    </style>
</head>
<body>
    <h1>代理功能测试页面</h1>
    <p>此页面用于测试Chrome扩展的API代理功能。请确保已加载并启用扩展。</p>

    <div class="test-section">
        <h3>测试1: 基本API请求</h3>
        <button onclick="testBasicAPI()">测试 /api/test 请求</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-section">
        <h3>测试2: 带认证的API请求</h3>
        <button onclick="testAuthAPI()">测试带Authorization头的请求</button>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-section">
        <h3>测试3: 非匹配路径请求</h3>
        <button onclick="testNonMatchingAPI()">测试 /other/path 请求</button>
        <div id="result3" class="result"></div>
    </div>

    <div class="test-section">
        <h3>测试4: POST请求</h3>
        <button onclick="testPostAPI()">测试 POST /api/data 请求</button>
        <div id="result4" class="result"></div>
    </div>

    <script>
        async function testBasicAPI() {
            const resultDiv = document.getElementById('result1');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('/api/test', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: await response.text()
                };
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testAuthAPI() {
            const resultDiv = document.getElementById('result2');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('/api/auth', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token-123'
                    }
                });
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: await response.text()
                };
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testNonMatchingAPI() {
            const resultDiv = document.getElementById('result3');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('/other/path', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: await response.text()
                };
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testPostAPI() {
            const resultDiv = document.getElementById('result4');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('/api/data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        test: 'data',
                        timestamp: new Date().toISOString()
                    })
                });
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: await response.text()
                };
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 页面加载时显示当前配置
        window.addEventListener('load', () => {
            console.log('测试页面已加载，可以开始测试代理功能');
            console.log('请确保：');
            console.log('1. Chrome扩展已加载并启用');
            console.log('2. 代理开关已打开');
            console.log('3. 目标主机已设置（如 http://127.0.0.1:9999）');
            console.log('4. 规则列表包含 /api/ 规则');
        });
    </script>
</body>
</html>
