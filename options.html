<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <div class="global-switch-container">
            <label class="switch">
                <input type="checkbox" id="globalEnableSwitch">
                <span class="slider round"></span>
            </label>
        </div>

        <div class="section">

        <div class="section">
            <h3>Target</h3>
            <input type="text" id="targetHostInput" placeholder="http://localhost:8080">
        </div>

        <div class="section">
            <h2>API Rules</h2>
            <div id="apiRulesContainer">
                <div class="api-rule">
                    <input type="text" class="api-pattern" placeholder="URL Pattern (e.g. https://ipublish-dev.unipus.cn/**)">
                    <button class="remove-rule-btn">×</button>
                </div>
            </div>
            <button id="addRuleBtn" class="btn">Add Rule</button>
        </div>

        <div class="section cache-management">
            <button id="clearCacheBtn" class="btn btn-danger">Clear Catch</button>
            <p id="statusMessage" class="status"></p>
        </div>
    </div>

    <script src="options.js"></script>
</body>
</html>