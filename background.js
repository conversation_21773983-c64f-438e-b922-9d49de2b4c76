// 为了提升性能，将配置和缓存保存在内存中
let settings = {
  globalEnable: false,
  targetHost: '',
  pathRules: [],
  pathRulesEnabled: []
};
let authCache = {}; // 认证缓存，现在直接与目标主机挂钩

// 数据迁移函数：将旧的apiRules格式迁移到新的settings格式
async function migrateData() {
  const data = await chrome.storage.local.get(['settings', 'authCache', 'apiRules', 'apiRulesEnabled']);

  // 如果存在旧的apiRules数据，迁移到settings中
  if (data.apiRules && (!data.settings || !data.settings.pathRules)) {
    const settings = data.settings || {};
    settings.pathRules = data.apiRules;
    settings.pathRulesEnabled = data.apiRulesEnabled || data.apiRules.map(() => true);
    settings.globalEnable = settings.globalEnable || false;
    settings.targetHost = settings.targetHost || '';

    // 保存迁移后的数据
    await chrome.storage.local.set({ settings });
    console.log('数据迁移完成：apiRules -> settings.pathRules');
  }

  // 加载最终的配置
  const finalData = await chrome.storage.local.get(['settings', 'authCache']);
  if (finalData.settings) settings = finalData.settings;
  if (finalData.authCache) authCache = finalData.authCache;
}

// 插件启动时，执行数据迁移和加载配置
migrateData();

// 监听storage变化，实时更新配置
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    if (changes.settings) {
      settings = changes.settings.newValue || {
        globalEnable: false,
        targetHost: '',
        pathRules: [],
        pathRulesEnabled: []
      };
    }
    if (changes.authCache) {
      authCache = changes.authCache.newValue || {};
    }
  }
});

// 设置验证和标准化函数
function validateAndNormalizeSettings(inputSettings) {
  const normalized = {
    globalEnable: Boolean(inputSettings.globalEnable),
    targetHost: String(inputSettings.targetHost || ''),
    pathRules: Array.isArray(inputSettings.pathRules) ? inputSettings.pathRules : [],
    pathRulesEnabled: Array.isArray(inputSettings.pathRulesEnabled) ? inputSettings.pathRulesEnabled : []
  };

  // 确保pathRulesEnabled数组长度与pathRules一致
  while (normalized.pathRulesEnabled.length < normalized.pathRules.length) {
    normalized.pathRulesEnabled.push(true);
  }
  while (normalized.pathRulesEnabled.length > normalized.pathRules.length) {
    normalized.pathRulesEnabled.pop();
  }

  return normalized;
}

// 监听来自选项页或内容脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'SETTINGS_UPDATED') {
    settings = validateAndNormalizeSettings(message.payload); // 验证并更新内存中的配置
    sendResponse({ success: true }); // 确认消息已接收
  } else if (message.type === 'PROXY_FETCH') {
    // 异步处理fetch请求
    handleFetch(message).then(sendResponse).catch(error => {
      console.error('处理fetch请求时出错:', error);
      sendResponse({ error: true, details: error.message });
    });
  } else if (message.type === 'CLEAR_AUTH_CACHE') {
    // 清除认证缓存
    authCache = {};
    sendResponse({ success: true });
  } else if (message.type === 'GET_SETTINGS') {
    // 获取当前设置
    sendResponse({ settings: settings, authCache: authCache });
  }
  return true; // 返回true，表示将异步发送响应
});

// 核心的fetch处理函数
async function handleFetch(request) {
  // 1. 检查全局开关是否开启
  if (!settings.globalEnable) {
    return { action: 'PROCEED_ORIGINAL' }; // 未开启则不处理
  }

  const reqUrl = new URL(request.payload.url);
  const requestPath = reqUrl.pathname + reqUrl.search; // 获取路径和查询参数

  // 2. 检查URL路径是否匹配规则
  // 使用settings中的规则进行匹配
  const pathRules = settings.pathRules || [];
  const pathRulesEnabled = settings.pathRulesEnabled || pathRules.map(() => true);

  // 只使用启用的规则进行匹配
  const enabledRules = pathRules.filter((rule, index) => pathRulesEnabled[index]);
  const isPathMatched = enabledRules.length === 0 || // 如果没有启用的规则，则匹配所有
                        enabledRules.some(rule => requestPath.startsWith(rule));

  if (!isPathMatched) {
    return { action: 'PROCEED_ORIGINAL' }; // 路径不匹配则不处理
  }

  // 3. 检查目标主机是否设置
  if (!settings.targetHost) {
    console.warn('开发代理：未设置目标主机，请求将不会被代理。');
    return { action: 'PROCEED_ORIGINAL' };
  }

  // 4. 构建目标URL
  // 假设目标主机是完整的URL，例如 http://localhost:8080
  const targetBaseUrl = new URL(settings.targetHost);
  const targetUrl = new URL(requestPath, targetBaseUrl.href); // 使用相对路径构建完整URL

  // 5. 附加缓存的Authorization头
  const headers = new Headers(request.payload.config.headers);
  // 认证缓存现在直接与目标主机挂钩
  if (authCache[settings.targetHost]) {
    headers.set('Authorization', authCache[settings.targetHost]);
  }

  // 如果请求本身包含Authorization头，也缓存它
  const originalAuth = headers.get('Authorization');
  if (originalAuth && !authCache[settings.targetHost]) {
    authCache[settings.targetHost] = originalAuth;
    chrome.storage.local.set({ authCache }); // 持久化保存
  }

  try {
    // 6. 执行真正的网络请求
    const response = await fetch(targetUrl.toString(), {
      method: request.payload.config.method,
      headers: headers,
      body: request.payload.config.body
    });

    // 7. 检查响应中是否有新的认证信息（如Set-Authorization自定义头）
    if (response.headers.has('Set-Authorization')) {
      authCache[settings.targetHost] = response.headers.get('Set-Authorization');
      chrome.storage.local.set({ authCache }); // 持久化保存
    }
    // 也检查标准的Authorization头（虽然不常见）
    if (response.headers.has('Authorization')) {
      authCache[settings.targetHost] = response.headers.get('Authorization');
      chrome.storage.local.set({ authCache }); // 持久化保存
    }

    // 8. 将完整的响应数据打包返回给页面中的拦截器
    return {
      action: 'PROXY_RESPONSE',
      data: {
        body: await response.text(),
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()) // 将Headers对象转为普通对象
      }
    };
  } catch (error) {
    console.error('代理请求失败:', error);
    return { error: true, details: error.message };
  }
}