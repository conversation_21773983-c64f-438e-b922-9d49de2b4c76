// 为了提升性能，将配置和缓存保存在内存中
let settings = { globalEnable: false, targetHost: '', pathRules: [] };
let authCache = {}; // 认证缓存，现在直接与目标主机挂钩

// 插件启动时，从storage加载配置
chrome.storage.local.get(['settings', 'authCache'], (data) => {
  if (data.settings) settings = data.settings;
  if (data.authCache) authCache = data.authCache;
});

// 监听来自选项页或内容脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'SETTINGS_UPDATED') {
    settings = message.payload; // 实时更新内存中的配置
    sendResponse({ success: true }); // 确认消息已接收
  } else if (message.type === 'PROXY_FETCH') {
    // 异步处理fetch请求
    handleFetch(message).then(sendResponse);
  }
  return true; // 返回true，表示将异步发送响应
});

// 核心的fetch处理函数
async function handleFetch(request) {
  // 1. 检查全局开关是否开启
  if (!settings.globalEnable) {
    return { action: 'PROCEED_ORIGINAL' }; // 未开启则不处理
  }

  const reqUrl = new URL(request.payload.url);
  const requestPath = reqUrl.pathname + reqUrl.search; // 获取路径和查询参数

  // 2. 检查URL路径是否匹配规则
  const isPathMatched = settings.pathRules.length === 0 || // 如果没有设置规则，则匹配所有
                        settings.pathRules.some(rule => requestPath.startsWith(rule));

  if (!isPathMatched) {
    return { action: 'PROCEED_ORIGINAL' }; // 路径不匹配则不处理
  }

  // 3. 检查目标主机是否设置
  if (!settings.targetHost) {
    console.warn('开发代理：未设置目标主机，请求将不会被代理。');
    return { action: 'PROCEED_ORIGINAL' };
  }

  // 4. 构建目标URL
  // 假设目标主机是完整的URL，例如 http://localhost:8080
  const targetBaseUrl = new URL(settings.targetHost);
  const targetUrl = new URL(requestPath, targetBaseUrl.href); // 使用相对路径构建完整URL

  // 5. 附加缓存的Authorization头
  const headers = new Headers(request.payload.config.headers);
  // 认证缓存现在直接与目标主机挂钩
  if (authCache[settings.targetHost]) {
    headers.set('Authorization', authCache[settings.targetHost]);
  }

  try {
    // 6. 执行真正的网络请求
    const response = await fetch(targetUrl.toString(), {
      method: request.payload.config.method,
      headers: headers,
      body: request.payload.config.body
    });

    // 7. 检查响应中是否有Authorization头，并进行缓存
    if (response.headers.has('Authorization')) {
      authCache[settings.targetHost] = response.headers.get('Authorization');
      chrome.storage.local.set({ authCache }); // 持久化保存
    }

    // 8. 将完整的响应数据打包返回给页面中的拦截器
    return {
      action: 'PROXY_RESPONSE',
      data: {
        body: await response.text(),
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()) // 将Headers对象转为普通对象
      }
    };
  } catch (error) {
    console.error('代理请求失败:', error);
    return { error: true, details: error.message };
  }
}