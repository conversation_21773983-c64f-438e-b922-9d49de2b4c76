<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理扩展单元测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-suite {
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #ccc;
        }
        .test-case.pass {
            border-left-color: #4caf50;
            background-color: #f1f8e9;
        }
        .test-case.fail {
            border-left-color: #f44336;
            background-color: #ffebee;
        }
        .test-case.pending {
            border-left-color: #ff9800;
            background-color: #fff3e0;
        }
        .test-result {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .test-details {
            font-size: 0.9em;
            color: #666;
        }
        .summary {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <h1>代理扩展单元测试</h1>
    <p>这个页面包含了Chrome扩展各个模块的单元测试。</p>
    
    <button onclick="runAllTests()">运行所有测试</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="test-results"></div>
    
    <div class="summary" id="summary" style="display: none;">
        <h3>测试总结</h3>
        <div id="summary-content"></div>
    </div>

    <script>
        // 测试框架
        class TestRunner {
            constructor() {
                this.tests = [];
                this.results = [];
            }
            
            describe(suiteName, testFn) {
                const suite = { name: suiteName, tests: [] };
                const currentSuite = suite;
                
                const it = (testName, testFn) => {
                    currentSuite.tests.push({ name: testName, fn: testFn });
                };
                
                const expect = (actual) => ({
                    toBe: (expected) => {
                        if (actual !== expected) {
                            throw new Error(`Expected ${expected}, but got ${actual}`);
                        }
                    },
                    toEqual: (expected) => {
                        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                            throw new Error(`Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`);
                        }
                    },
                    toBeTruthy: () => {
                        if (!actual) {
                            throw new Error(`Expected truthy value, but got ${actual}`);
                        }
                    },
                    toBeFalsy: () => {
                        if (actual) {
                            throw new Error(`Expected falsy value, but got ${actual}`);
                        }
                    },
                    toContain: (expected) => {
                        if (!actual.includes(expected)) {
                            throw new Error(`Expected ${actual} to contain ${expected}`);
                        }
                    }
                });
                
                // 执行测试套件定义
                testFn(it, expect);
                this.tests.push(suite);
            }
            
            async run() {
                this.results = [];
                const resultsContainer = document.getElementById('test-results');
                resultsContainer.innerHTML = '';
                
                for (const suite of this.tests) {
                    const suiteDiv = document.createElement('div');
                    suiteDiv.className = 'test-suite';
                    suiteDiv.innerHTML = `<h3>${suite.name}</h3>`;
                    
                    const suiteResults = { name: suite.name, passed: 0, failed: 0, tests: [] };
                    
                    for (const test of suite.tests) {
                        const testDiv = document.createElement('div');
                        testDiv.className = 'test-case pending';
                        
                        try {
                            await test.fn();
                            testDiv.className = 'test-case pass';
                            testDiv.innerHTML = `
                                <div class="test-result">✓ ${test.name}</div>
                                <div class="test-details">通过</div>
                            `;
                            suiteResults.passed++;
                            suiteResults.tests.push({ name: test.name, status: 'pass' });
                        } catch (error) {
                            testDiv.className = 'test-case fail';
                            testDiv.innerHTML = `
                                <div class="test-result">✗ ${test.name}</div>
                                <div class="test-details">失败: ${error.message}</div>
                            `;
                            suiteResults.failed++;
                            suiteResults.tests.push({ name: test.name, status: 'fail', error: error.message });
                        }
                        
                        suiteDiv.appendChild(testDiv);
                    }
                    
                    resultsContainer.appendChild(suiteDiv);
                    this.results.push(suiteResults);
                }
                
                this.showSummary();
            }
            
            showSummary() {
                const summary = document.getElementById('summary');
                const summaryContent = document.getElementById('summary-content');
                
                const totalPassed = this.results.reduce((sum, suite) => sum + suite.passed, 0);
                const totalFailed = this.results.reduce((sum, suite) => sum + suite.failed, 0);
                const totalTests = totalPassed + totalFailed;
                
                summaryContent.innerHTML = `
                    <p><strong>总测试数:</strong> ${totalTests}</p>
                    <p><strong>通过:</strong> ${totalPassed}</p>
                    <p><strong>失败:</strong> ${totalFailed}</p>
                    <p><strong>成功率:</strong> ${totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0}%</p>
                `;
                
                summary.style.display = 'block';
            }
        }
        
        const testRunner = new TestRunner();
        
        // 模拟Chrome API
        const mockChrome = {
            storage: {
                local: {
                    data: {},
                    get: function(keys, callback) {
                        const result = {};
                        if (Array.isArray(keys)) {
                            keys.forEach(key => {
                                if (this.data[key] !== undefined) {
                                    result[key] = this.data[key];
                                }
                            });
                        } else if (typeof keys === 'string') {
                            if (this.data[keys] !== undefined) {
                                result[keys] = this.data[keys];
                            }
                        }
                        callback(result);
                    },
                    set: function(items, callback) {
                        Object.assign(this.data, items);
                        if (callback) callback();
                    }
                }
            },
            runtime: {
                sendMessage: function(message, callback) {
                    // 模拟消息发送
                    if (callback) {
                        setTimeout(() => callback({ success: true }), 10);
                    }
                }
            }
        };
        
        // 设置验证函数（从background.js复制）
        function validateAndNormalizeSettings(inputSettings) {
            const normalized = {
                globalEnable: Boolean(inputSettings.globalEnable),
                targetHost: String(inputSettings.targetHost || ''),
                pathRules: Array.isArray(inputSettings.pathRules) ? inputSettings.pathRules : [],
                pathRulesEnabled: Array.isArray(inputSettings.pathRulesEnabled) ? inputSettings.pathRulesEnabled : []
            };
            
            // 确保pathRulesEnabled数组长度与pathRules一致
            while (normalized.pathRulesEnabled.length < normalized.pathRules.length) {
                normalized.pathRulesEnabled.push(true);
            }
            while (normalized.pathRulesEnabled.length > normalized.pathRules.length) {
                normalized.pathRulesEnabled.pop();
            }
            
            return normalized;
        }
        
        // URL匹配函数（从background.js复制）
        function checkPathMatch(requestPath, pathRules, pathRulesEnabled) {
            const enabledRules = pathRules.filter((rule, index) => pathRulesEnabled[index]);
            return enabledRules.length === 0 || enabledRules.some(rule => requestPath.startsWith(rule));
        }
        
        function runAllTests() {
            testRunner.run();
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('summary').style.display = 'none';
        }

        // 定义测试用例
        testRunner.describe('设置验证和标准化', (it, expect) => {
            it('应该正确验证和标准化空设置', () => {
                const result = validateAndNormalizeSettings({});
                expect(result.globalEnable).toBe(false);
                expect(result.targetHost).toBe('');
                expect(result.pathRules).toEqual([]);
                expect(result.pathRulesEnabled).toEqual([]);
            });

            it('应该正确处理基本设置', () => {
                const input = {
                    globalEnable: true,
                    targetHost: 'http://localhost:8080',
                    pathRules: ['/api/', '/v1/'],
                    pathRulesEnabled: [true, false]
                };
                const result = validateAndNormalizeSettings(input);
                expect(result).toEqual(input);
            });

            it('应该自动补齐pathRulesEnabled数组', () => {
                const input = {
                    pathRules: ['/api/', '/v1/', '/v2/'],
                    pathRulesEnabled: [true]
                };
                const result = validateAndNormalizeSettings(input);
                expect(result.pathRulesEnabled).toEqual([true, true, true]);
            });

            it('应该自动截断过长的pathRulesEnabled数组', () => {
                const input = {
                    pathRules: ['/api/'],
                    pathRulesEnabled: [true, false, true]
                };
                const result = validateAndNormalizeSettings(input);
                expect(result.pathRulesEnabled).toEqual([true]);
            });
        });

        testRunner.describe('URL路径匹配', (it, expect) => {
            it('应该匹配启用的规则', () => {
                const pathRules = ['/api/', '/v1/', '/admin/'];
                const pathRulesEnabled = [true, false, true];

                expect(checkPathMatch('/api/users', pathRules, pathRulesEnabled)).toBeTruthy();
                expect(checkPathMatch('/admin/settings', pathRules, pathRulesEnabled)).toBeTruthy();
                expect(checkPathMatch('/v1/data', pathRules, pathRulesEnabled)).toBeFalsy();
                expect(checkPathMatch('/other/path', pathRules, pathRulesEnabled)).toBeFalsy();
            });

            it('空规则应该匹配所有路径', () => {
                expect(checkPathMatch('/any/path', [], [])).toBeTruthy();
            });

            it('所有规则都禁用时应该匹配所有路径', () => {
                const pathRules = ['/api/', '/v1/'];
                const pathRulesEnabled = [false, false];
                expect(checkPathMatch('/any/path', pathRules, pathRulesEnabled)).toBeTruthy();
            });
        });

        testRunner.describe('Chrome Storage 模拟', (it, expect) => {
            it('应该能够存储和读取数据', (done) => {
                const testData = { test: 'value' };
                mockChrome.storage.local.set(testData, () => {
                    mockChrome.storage.local.get('test', (result) => {
                        expect(result.test).toBe('value');
                        done();
                    });
                });
            });

            it('应该能够存储复杂对象', (done) => {
                const settings = {
                    globalEnable: true,
                    targetHost: 'http://localhost:8080',
                    pathRules: ['/api/', '/v1/']
                };
                mockChrome.storage.local.set({ settings }, () => {
                    mockChrome.storage.local.get('settings', (result) => {
                        expect(result.settings).toEqual(settings);
                        done();
                    });
                });
            });
        });

        testRunner.describe('数据迁移逻辑', (it, expect) => {
            it('应该正确迁移旧的apiRules格式', () => {
                const oldData = {
                    apiRules: ['/api/', '/v1/'],
                    apiRulesEnabled: [true, false]
                };

                // 模拟迁移逻辑
                const migratedSettings = {
                    globalEnable: false,
                    targetHost: '',
                    pathRules: oldData.apiRules,
                    pathRulesEnabled: oldData.apiRulesEnabled
                };

                expect(migratedSettings.pathRules).toEqual(['/api/', '/v1/']);
                expect(migratedSettings.pathRulesEnabled).toEqual([true, false]);
            });
        });

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            console.log('单元测试页面已加载');
        });
    </script>
</body>
</html>
