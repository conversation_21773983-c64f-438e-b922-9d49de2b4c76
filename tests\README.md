# 代理扩展测试套件

这个目录包含了Chrome代理扩展的完整测试套件，包括单元测试和集成测试。

## 📁 文件结构

```
tests/
├── README.md              # 测试说明文档
├── unit-tests.html        # 单元测试页面
└── integration-tests.html # 集成测试页面
```

## 🧪 测试类型

### 1. 单元测试 (unit-tests.html)

测试独立的函数和模块，不依赖Chrome扩展环境。

**测试内容:**
- 设置验证和标准化函数
- URL路径匹配逻辑
- 数据迁移逻辑
- Chrome Storage模拟

**运行方式:**
```bash
# 直接在浏览器中打开
open tests/unit-tests.html
```

### 2. 集成测试 (integration-tests.html)

测试完整的扩展功能流程，需要在Chrome扩展环境中运行。

**测试内容:**
- 扩展连接测试
- 设置存储测试
- 规则匹配测试
- 代理转发测试
- 认证缓存测试
- 完整流程测试

**运行方式:**
1. 确保Chrome扩展已加载
2. 在扩展中打开测试页面，或者
3. 将测试页面添加到扩展的web_accessible_resources中

## 🚀 如何运行测试

### 方法1: 直接运行单元测试
```bash
# 在浏览器中打开单元测试
open tests/unit-tests.html
```

### 方法2: 在扩展环境中运行集成测试
1. 加载Chrome扩展到开发者模式
2. 打开扩展的popup或options页面
3. 在控制台中运行:
   ```javascript
   window.open(chrome.runtime.getURL('tests/integration-tests.html'));
   ```

### 方法3: 添加测试页面到扩展
在`manifest.json`中添加:
```json
{
  "web_accessible_resources": [
    {
      "resources": ["tests/*.html"],
      "matches": ["<all_urls>"]
    }
  ]
}
```

## 📋 测试检查清单

### 单元测试检查项
- [ ] 设置验证函数正确处理空输入
- [ ] 设置验证函数正确标准化数据
- [ ] pathRulesEnabled数组长度自动调整
- [ ] URL路径匹配逻辑正确
- [ ] 空规则匹配所有路径
- [ ] 禁用规则不参与匹配
- [ ] Chrome Storage模拟正常工作

### 集成测试检查项
- [ ] 扩展API连接正常
- [ ] 设置能够正确存储和读取
- [ ] 规则匹配逻辑在实际环境中工作
- [ ] 代理转发功能正常
- [ ] 认证缓存功能正常
- [ ] 完整工作流程无错误

## 🔧 测试配置

### 测试服务器设置
如果需要测试代理转发功能，可以使用项目中的测试服务器:

```bash
cd proxy-log
python proxy-log.py
```

服务器将在 `http://127.0.0.1:9999` 启动。

### 测试数据
测试使用的默认配置:
```javascript
{
  globalEnable: true,
  targetHost: 'http://localhost:8080',
  pathRules: ['/api/', '/v1/'],
  pathRulesEnabled: [true, false]
}
```

## 📊 测试报告

测试运行后会显示:
- 总测试数
- 通过的测试数
- 失败的测试数
- 成功率百分比
- 详细的错误信息

## 🐛 故障排除

### 常见问题

1. **"Chrome扩展API不可用"**
   - 确保在Chrome扩展环境中运行集成测试
   - 检查manifest.json权限设置

2. **"扩展连接失败"**
   - 确保扩展已正确加载
   - 检查background.js是否正常运行
   - 查看扩展的错误日志

3. **"代理转发测试失败"**
   - 确保目标服务器正在运行
   - 检查网络连接
   - 验证CORS设置

4. **"设置存储失败"**
   - 检查storage权限
   - 确保没有其他扩展冲突

### 调试技巧

1. 打开Chrome开发者工具
2. 查看Console标签页的错误信息
3. 检查Network标签页的请求
4. 使用chrome://extensions查看扩展状态

## 📝 添加新测试

### 添加单元测试
```javascript
testRunner.describe('新测试套件', (it, expect) => {
    it('应该测试某个功能', () => {
        // 测试代码
        expect(actual).toBe(expected);
    });
});
```

### 添加集成测试
```javascript
async function testNewFeature() {
    showResult('result-id', '正在测试...', 'info');
    try {
        // 测试代码
        showSuccess('result-id', '测试通过！');
    } catch (error) {
        showError('result-id', error);
    }
}
```

## 🎯 测试最佳实践

1. **独立性**: 每个测试应该独立运行
2. **可重复性**: 测试结果应该一致
3. **清晰性**: 测试名称应该描述测试内容
4. **完整性**: 覆盖正常和异常情况
5. **维护性**: 测试代码应该易于理解和修改

## 📚 相关文档

- [Chrome扩展开发文档](https://developer.chrome.com/docs/extensions/)
- [Chrome Storage API](https://developer.chrome.com/docs/extensions/reference/storage/)
- [Chrome Runtime API](https://developer.chrome.com/docs/extensions/reference/runtime/)
