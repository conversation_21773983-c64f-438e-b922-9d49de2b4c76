(async () => {
  // 从storage中读取配置
  const { settings } = await chrome.storage.local.get('settings');
  // 如果全局开关开启，则显示提示
  if (settings && settings.globalEnable) {
    const indicator = document.createElement('div');
    indicator.textContent = '开发代理已启用';
    // 设置样式
    indicator.style.cssText = `
      position: fixed; top: 15px; right: 15px; 
      background-color: #ff5722; /* 更改为橙色背景 */
      color: white;
      padding: 8px 15px; 
      border-radius: 5px; 
      font-size: 14px; 
      font-weight: bold; /* 加粗字体 */
      z-index: 99999;
      box-shadow: 0 2px 15px rgba(255,87,34,0.3); /* 更明显的阴影 */
      border: 1px solid #e64a19; /* 添加边框 */
      transition: opacity 0.5s ease-in-out, transform 0.3s ease;
      transform: translateY(-10px); /* 初始位置偏上 */
      animation: pulse 2s infinite; /* 添加脉动效果 */
    `;
    
    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(255,87,34,0.4); }
        70% { box-shadow: 0 0 0 10px rgba(255,87,34,0); }
        100% { box-shadow: 0 0 0 0 rgba(255,87,34,0); }
      }
    `;
    document.head.appendChild(style);
    // 延时显示，避免页面加载初期的闪烁
    setTimeout(() => {
        document.body.appendChild(indicator);
    }, 500);
    
    // 3秒后自动淡出并移除
    setTimeout(() => {
      indicator.style.opacity = '0';
      setTimeout(() => indicator.remove(), 500);
    }, 3500);
  }
})();

const s = document.createElement('script');
s.src = chrome.runtime.getURL('interceptor.js');
document.documentElement.appendChild(s);
// 脚本加载后立即移除DOM节点，保持页面整洁
s.onload = () => s.remove();
