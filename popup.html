<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API-TOOL</title>
    <style>
        :root {
            --primary-color: #46ad24;
            --secondary-color: #554bd6;
            --success-color: #4cc9f0;
            --danger-color: #f44336;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --tab-height: 36px;
            --light-bg: #f5f7fa;
            --card-bg: #fff;
            --border: #e0e0e0;
            --shadow: 0 2px 8px rgba(0,0,0,0.06);
            --input-bg: #fff;
        }
        
        .header {
            position: relative;
        }
        
        .proxy-toggle {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            width: 300px;
            margin: 0;
            padding: 0;
            background: var(--light-bg);
            color: #222;
        }
        
        .container {
            padding: 10px 8px 8px 8px;
        }
        
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .logo {
            width: 40px;
            height: 40px;
            margin-right: 10px;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
        }
        
        .status-panel {
            margin-bottom: 16px;
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-label {
            font-size: 14px;
            color: #6c757d;
        }
        
        .status-value {
            font-weight: 600;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            text-align: center;
            margin: 12px 0;
            gap: 8px;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-options {
            background-color: white;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            margin-top: 8px;
        }

        .btn-options:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }
        
        .rule-list {
            padding: 10px 8px 8px 8px;
            margin-top: 6px;
            background: #e9eef6;
            border-radius: 12px;
            box-shadow: var(--shadow);
            border: none;
        }
        
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 700;
            font-size: 14px;
            margin-bottom: 6px;
            letter-spacing: 0.2px;
            color: #b0b0b0;
        }
        
        .rules-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .rules-list {
            max-height: 350px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 6px;
        }
        
        .rule-item {
            display: flex;
            align-items: center;
            background: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(70,173,36,0.06);
            border: 1px solid var(--border);
            padding: 6px 10px 6px 14px;
            gap: 8px;
            transition: box-shadow 0.2s, border 0.2s;
        }
        
        .rule-item:hover {
            box-shadow: 0 4px 16px rgba(70,173,36,0.13);
            border: 1.5px solid var(--primary-color);
        }
        
        .rule-item-content, .rule-input {
            flex: 1;
            padding: 10px 12px;
            border: 1.5px solid var(--border);
            border-radius: 6px;
            font-size: 15px;
            background: #fff;
            transition: border 0.2s;
            outline: none;
            box-sizing: border-box;
            height: 36px;
            display: flex;
            align-items: center;
        }
        .rule-item-content {
            border: 1.5px solid var(--border);
            color: #222;
            word-break: break-all;
            cursor: default;
        }
        .rule-item-content:focus, .rule-input:focus {
            border: 1.5px solid var(--primary-color);
        }
        
        .rule-actions {
            display: flex;
            align-items: center;
        }
        
        .rule-btn {
            margin-left: 6px;
            width: 20px;
            height: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 50%;
            color: #bbb;
            font-size: 14px;
            font-weight: normal;
            background: #f7f7f7;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s, color 0.2s, border 0.2s;
            z-index: 2;
            box-shadow: none;
            flex-shrink: 0;
        }
        
        .rule-btn.delete-btn {
            background: #f9eaea;
            color: #c82333;
            border-color: #f5c6cb;
        }
        
        .rule-btn.delete-btn:hover {
            background: #f44336;
            color: #fff;
            border-color: #f44336;
        }
        
        .rule-btn.add-btn {
            background: #eaf9ea;
            color: #388e3c;
            border-color: #b7e1cd;
        }
        
        .rule-btn.add-btn:hover {
            background: #46ad24;
            color: #fff;
            border-color: #46ad24;
        }
        
        .rule-btn.enable-btn {
            background: #46ad24;
            margin-right: 6px;
        }
        .rule-btn.enable-btn.disabled {
            background: #bdbdbd;
        }
        .rule-btn.enable-btn:hover:not(.disabled) {
            background: #388e3c;
        }
        
        .edit-row {
            position: relative;
            display: flex;
            align-items: center;
            margin-top: 0;
            gap: 0;
        }
        
        .rule-input {
            width: 100%;
            padding-right: 38px;
        }
        
        ::-webkit-scrollbar {
            width: 7px;
            background: #e9eef6;
            border-radius: 8px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c3cfe2;
            border-radius: 8px;
        }
        
        .footer {
            font-size: 11px;
            text-align: center;
            color: #6c757d;
            margin-top: 24px;
            padding-top: 12px;
            border-top: 1px solid rgba(0,0,0,0.05);
        }
        
        .input-row {
            display: flex;
            align-items: center;
            margin-bottom: 2px;
        }
        
        .rule-input, .rule-item-content {
            flex: 1;
            padding: 4px 8px;
            border: 1.5px solid var(--border);
            border-radius: 8px;
            font-size: 13px;
            background: var(--input-bg);
            transition: border 0.2s;
            outline: none;
            box-sizing: border-box;
            height: 30px;
            display: flex;
            align-items: center;
        }
        
        .rule-input:focus, .rule-item-content:focus {
            border: 1.5px solid var(--primary-color);
        }
        
        .rule-item-content {
            color: #222;
            word-break: break-all;
            cursor: default;
            user-select: text;
        }
        
        .rule-btn {
            margin-left: 4px;
            width: 18px;
            height: 18px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="icons/icon48.png" alt="Logo" class="logo">
            <h1 class="title">aTool</h1>
            <div class="proxy-toggle">
                <label class="switch">
                    <input type="checkbox" id="proxySwitch">
                    <span class="slider"></span>
                </label>
            </div>
        </div>
        
        <div class="status-panel">
            <div class="status-row">
                <span class="status-label">目标主机</span>
                <input type="text" id="targetHost" class="rule-input" value="http://127.0.0.1:9999" style="width: 60%;">
            </div>
        </div>
        
        <div class="rule-list">
            <div class="rule-header">
                <span>规则列表</span>
                <span id="ruleCount">0 条规则</span>
            </div>
            <div class="rules-container">
                <div class="rules-list" id="rulesList">
                    <!-- 规则项将在这里动态添加，每项结构为.input-row -->
                </div>
                <div class="input-row">
                    <input type="text" class="rule-input" placeholder="输入新规则" id="newRuleInput">
                    <button class="rule-btn add-btn" title="添加">+</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>