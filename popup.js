document.addEventListener('DOMContentLoaded', () => {
    const rulesList = document.getElementById('rulesList');
    const newRuleInput = document.getElementById('newRuleInput');
    const ruleCount = document.getElementById('ruleCount');
    const addBtn = document.querySelector('.add-btn');
    const proxySwitch = document.getElementById('proxySwitch');
    const targetHost = document.getElementById('targetHost');
    const clearCacheBtn = document.getElementById('clearCacheBtn');

    // 渲染规则列表，支持启用/停止
    function renderRules(rules) {
        rulesList.innerHTML = '';
        chrome.storage.local.get('apiRulesEnabled', (data) => {
            let enabledList = Array.isArray(data.apiRulesEnabled) ? data.apiRulesEnabled : rules.map(() => true);
            while (enabledList.length < rules.length) enabledList.push(true);
            while (enabledList.length > rules.length) enabledList.pop();
            rules.forEach((rule, idx) => {
                const row = document.createElement('div');
                row.className = 'input-row';
                const enabled = enabledList[idx];
                row.innerHTML = `
                    <div class="rule-input rule-item-content" data-idx="${idx}" style="opacity:${enabled ? 1 : 0.5};cursor:pointer;">${rule}</div>
                    <button class="rule-btn delete-btn" data-idx="${idx}" title="删除">×</button>
                `;
                rulesList.appendChild(row);
            });
            ruleCount.textContent = (rules?.length || 0) + ' 条规则';
            // 点击内容区切换启用/停用
            rulesList.querySelectorAll('.rule-item-content').forEach(div => {
                div.addEventListener('click', function() {
                    const idx = parseInt(this.getAttribute('data-idx'));
                    enabledList[idx] = !enabledList[idx];
                    chrome.storage.local.set({ apiRulesEnabled: enabledList }, () => {
                        renderRules(rules);
                        saveSettings(); // 同步更新settings
                    });
                });
            });
            // 删除事件
            rulesList.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const idx = parseInt(this.getAttribute('data-idx'));
                    // 重新从storage读取最新数据再删除
                    chrome.storage.local.get(['apiRules', 'apiRulesEnabled'], (data) => {
                        let rules = data.apiRules || [];
                        let enabledList = Array.isArray(data.apiRulesEnabled) ? data.apiRulesEnabled : rules.map(() => true);
                        rules.splice(idx, 1);
                        enabledList.splice(idx, 1);
                        chrome.storage.local.set({ apiRules: rules, apiRulesEnabled: enabledList }, () => {
                            saveSettings(); // 同步更新settings
                        });
                    });
                });
            });
        });
    }

    // 加载规则
    function loadRules() {
        chrome.storage.local.get(['apiRules', 'settings'], (data) => {
            renderRules(data.apiRules || []);
            // 同步主机和开关
            if (data.settings) {
                proxySwitch.checked = data.settings.globalEnable || false;
                targetHost.value = data.settings.targetHost || '';
            }
        });
    }

    // 保存设置到storage
    function saveSettings() {
        chrome.storage.local.get(['settings'], (data) => {
            const settings = data.settings || {};
            settings.globalEnable = proxySwitch.checked;
            settings.targetHost = targetHost.value.trim();

            // 获取当前规则作为pathRules
            chrome.storage.local.get(['apiRules'], (ruleData) => {
                settings.pathRules = ruleData.apiRules || [];

                chrome.storage.local.set({ settings }, () => {
                    // 通知background.js更新设置
                    chrome.runtime.sendMessage({
                        type: 'SETTINGS_UPDATED',
                        payload: settings
                    });
                });
            });
        });
    }

    // 添加规则
    addBtn.addEventListener('click', () => {
        const ruleText = newRuleInput.value.trim();
        if (!ruleText) return;
        chrome.storage.local.get(['apiRules', 'apiRulesEnabled'], (data) => {
            let rules = data.apiRules || [];
            let enabledList = Array.isArray(data.apiRulesEnabled) ? data.apiRulesEnabled : rules.map(() => true);
            rules.push(ruleText);
            enabledList.push(true);
            chrome.storage.local.set({ apiRules: rules, apiRulesEnabled: enabledList }, () => {
                newRuleInput.value = '';
                saveSettings(); // 同步更新settings
            });
        });
    });

    // 监听代理开关变化
    proxySwitch.addEventListener('change', saveSettings);

    // 监听目标主机输入变化
    targetHost.addEventListener('input', saveSettings);

    // 清除认证缓存
    clearCacheBtn.addEventListener('click', () => {
        chrome.storage.local.set({ authCache: {} }, () => {
            // 通知background.js更新缓存
            chrome.runtime.sendMessage({
                type: 'CLEAR_AUTH_CACHE'
            });

            // 显示成功提示
            const originalText = clearCacheBtn.textContent;
            clearCacheBtn.textContent = '已清除';
            clearCacheBtn.style.backgroundColor = '#4cc9f0';
            setTimeout(() => {
                clearCacheBtn.textContent = originalText;
                clearCacheBtn.style.backgroundColor = '';
            }, 1500);
        });
    });

    // 监听 storage 变化自动刷新
    chrome.storage.onChanged.addListener((changes) => {
        if (changes.apiRules) {
            renderRules(changes.apiRules.newValue || []);
        }
        if (changes.settings) {
            proxySwitch.checked = changes.settings.newValue.globalEnable || false;
            targetHost.value = changes.settings.newValue.targetHost || '';
        }
    });

    // 初始化
    loadRules();

    // 移除了对不存在DOM元素的引用

    // 确保background.js已加载
    if (!chrome.runtime?.sendMessage) {
        console.error('无法连接到background.js');
        document.body.innerHTML = '<p style="color:red;padding:15px;">插件初始化失败，请刷新页面重试</p>';
    }

    addBtn.innerHTML = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none"><path d="M10 4v12M4 10h12" stroke="white" stroke-width="2" stroke-linecap="round"/></svg>`;
});