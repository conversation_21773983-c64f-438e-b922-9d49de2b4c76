document.addEventListener('DOMContentLoaded', () => {
    const rulesList = document.getElementById('rulesList');
    const newRuleInput = document.getElementById('newRuleInput');
    const ruleCount = document.getElementById('ruleCount');
    const addBtn = document.querySelector('.add-btn');
    const proxySwitch = document.getElementById('proxySwitch');
    const targetHost = document.getElementById('targetHost');

    // 渲染规则列表，支持启用/停止
    function renderRules(rules) {
        rulesList.innerHTML = '';
        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            let enabledList = Array.isArray(settings.pathRulesEnabled) ? settings.pathRulesEnabled : rules.map(() => true);
            while (enabledList.length < rules.length) enabledList.push(true);
            while (enabledList.length > rules.length) enabledList.pop();
            rules.forEach((rule, idx) => {
                const row = document.createElement('div');
                row.className = 'input-row';
                const enabled = enabledList[idx];
                row.innerHTML = `
                    <div class="rule-input rule-item-content" data-idx="${idx}" data-full-text="${rule}" style="opacity:${enabled ? 1 : 0.5};cursor:pointer;" title="点击切换启用/禁用">${rule}</div>
                `;
                rulesList.appendChild(row);
            });
            ruleCount.textContent = (rules?.length || 0) + ' 条规则';
            // 点击内容区切换启用/停用
            rulesList.querySelectorAll('.rule-item-content').forEach(div => {
                div.addEventListener('click', function() {
                    const idx = parseInt(this.getAttribute('data-idx'));
                    enabledList[idx] = !enabledList[idx];
                    // 直接更新settings
                    chrome.storage.local.get('settings', (data) => {
                        const settings = data.settings || {};
                        settings.pathRulesEnabled = enabledList;
                        chrome.storage.local.set({ settings }, () => {
                            renderRules(rules);
                            // 通知background.js更新设置
                            chrome.runtime.sendMessage({
                                type: 'SETTINGS_UPDATED',
                                payload: settings
                            });
                        });
                    });
                });
            });
        });
    }

    // 加载规则
    function loadRules() {
        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            renderRules(settings.pathRules || []);
            // 同步主机和开关
            proxySwitch.checked = settings.globalEnable || false;
            targetHost.value = settings.targetHost || '';
        });
    }

    // 保存设置到storage
    function saveSettings() {
        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            settings.globalEnable = proxySwitch.checked;
            settings.targetHost = targetHost.value.trim();

            chrome.storage.local.set({ settings }, () => {
                // 通知background.js更新设置
                chrome.runtime.sendMessage({
                    type: 'SETTINGS_UPDATED',
                    payload: settings
                });
            });
        });
    }

    // 添加规则
    addBtn.addEventListener('click', () => {
        const ruleText = newRuleInput.value.trim();
        if (!ruleText) return;
        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            let rules = settings.pathRules || [];
            let enabledList = Array.isArray(settings.pathRulesEnabled) ? settings.pathRulesEnabled : rules.map(() => true);
            rules.push(ruleText);
            enabledList.push(true);
            settings.pathRules = rules;
            settings.pathRulesEnabled = enabledList;
            chrome.storage.local.set({ settings }, () => {
                newRuleInput.value = '';
                // 通知background.js更新设置
                chrome.runtime.sendMessage({
                    type: 'SETTINGS_UPDATED',
                    payload: settings
                });
            });
        });
    });

    // 监听代理开关变化
    proxySwitch.addEventListener('change', saveSettings);

    // 监听目标主机输入变化
    targetHost.addEventListener('input', saveSettings);

    // 监听 storage 变化自动刷新
    chrome.storage.onChanged.addListener((changes) => {
        if (changes.settings) {
            const newSettings = changes.settings.newValue || {};
            renderRules(newSettings.pathRules || []);
            proxySwitch.checked = newSettings.globalEnable || false;
            targetHost.value = newSettings.targetHost || '';
        }
    });

    // 初始化
    loadRules();

    // 移除了对不存在DOM元素的引用

    // 确保background.js已加载
    if (!chrome.runtime?.sendMessage) {
        console.error('无法连接到background.js');
        document.body.innerHTML = '<p style="color:red;padding:15px;">插件初始化失败，请刷新页面重试</p>';
    }

    addBtn.innerHTML = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none"><path d="M10 4v12M4 10h12" stroke="white" stroke-width="2" stroke-linecap="round"/></svg>`;
});